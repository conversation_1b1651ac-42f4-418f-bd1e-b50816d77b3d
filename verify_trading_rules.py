#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证买卖规则的执行
明确展示：预测上涨当日开盘买入，次日开盘卖出

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class TradingRulesVerification:
    """买卖规则验证系统"""
    
    def __init__(self, tech_strength_path, stock_data_path):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
    
    def load_and_merge_data(self):
        """加载并合并数据"""
        print("=" * 80)
        print("🔍 买卖规则验证系统")
        print("📋 买卖规则: 预测上涨当日开盘买入，次日开盘卖出")
        print("=" * 80)
        
        # 加载数据
        print("📂 加载数据...")
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"  技术强度数据: {len(tech_df)} 条")
        print(f"  股票交易数据: {len(stock_df)} 条")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 合并数据
        print("🔗 合并数据...")
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='inner',
            suffixes=('_tech', '_stock')
        )
        
        print(f"  合并后数据: {len(merged_df)} 条")
        return merged_df
    
    def simulate_trading_with_verification(self, df):
        """模拟交易并验证买卖规则"""
        print(f"\n💹 模拟交易过程 (验证买卖规则)...")
        
        trading_results = []
        verification_samples = []  # 保存一些样本用于验证
        
        sample_count = 0
        
        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            for i in range(len(group) - 1):
                current_row = group.iloc[i]
                next_row = group.iloc[i + 1]
                
                # 买卖规则执行
                buy_date = current_row['日期']  # 预测日期（当日）
                sell_date = next_row['日期']   # 次日
                
                buy_price = current_row['开盘价']  # 当日开盘价买入
                sell_price = next_row['开盘价']   # 次日开盘价卖出
                
                if buy_price > 0 and sell_price > 0:
                    return_rate = (sell_price - buy_price) / buy_price
                    profit_amount = sell_price - buy_price
                    
                    trading_record = {
                        '股票代码': stock_code,
                        '买入日期': buy_date,
                        '卖出日期': sell_date,
                        '买入价格_当日开盘': buy_price,
                        '卖出价格_次日开盘': sell_price,
                        '收益率': return_rate,
                        '盈利金额': profit_amount,
                        '是否盈利': return_rate > 0,
                        
                        # 预测依据的技术指标（当日的）
                        '技术强度': current_row['技术强度'],
                        '技术指标特征': current_row.get('技术指标特征', ''),
                        '趋势组合': current_row.get('趋势组合', ''),
                        '成交量是前一日几倍': current_row.get('成交量是前一日几倍', 0),
                        '日内股票标记': current_row.get('日内股票标记', ''),
                        
                        # 当日其他价格信息
                        '当日最高价': current_row['最高价'],
                        '当日最低价': current_row['最低价'],
                        '当日收盘价': current_row.get('收盘价_stock', current_row.get('收盘价', 0)),
                        
                        # 次日价格信息
                        '次日最高价': next_row['最高价'],
                        '次日最低价': next_row['最低价'],
                        '次日收盘价': next_row.get('收盘价_stock', next_row.get('收盘价', 0)),
                    }
                    
                    trading_results.append(trading_record)
                    
                    # 保存前100个样本用于验证展示
                    if sample_count < 100:
                        verification_samples.append(trading_record)
                        sample_count += 1
        
        trading_df = pd.DataFrame(trading_results)
        verification_df = pd.DataFrame(verification_samples)
        
        print(f"  生成交易记录: {len(trading_df)} 笔")
        
        if len(trading_df) > 0:
            win_rate = trading_df['是否盈利'].mean()
            avg_return = trading_df['收益率'].mean()
            total_profit = trading_df['盈利金额'].sum()
            
            print(f"  整体胜率: {win_rate:.3f} ({win_rate*100:.1f}%)")
            print(f"  平均收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")
            print(f"  总盈利金额: {total_profit:.2f}元")
        
        return trading_df, verification_df
    
    def verify_trading_samples(self, verification_df):
        """验证交易样本"""
        print(f"\n🔍 买卖规则验证 (展示前20个交易样本):")
        print(f"{'='*120}")
        print(f"{'序号':<4} {'股票代码':<10} {'买入日期':<12} {'卖出日期':<12} {'买入价':<8} {'卖出价':<8} {'收益率':<8} {'技术强度':<6} {'成交量倍数':<8}")
        print(f"{'='*120}")
        
        for i, (_, row) in enumerate(verification_df.head(20).iterrows(), 1):
            buy_date = row['买入日期'].strftime('%Y-%m-%d')
            sell_date = row['卖出日期'].strftime('%Y-%m-%d')
            buy_price = row['买入价格_当日开盘']
            sell_price = row['卖出价格_次日开盘']
            return_rate = row['收益率']
            tech_strength = row['技术强度']
            volume_ratio = row['成交量是前一日几倍']
            
            print(f"{i:<4} {row['股票代码']:<10} {buy_date:<12} {sell_date:<12} "
                  f"{buy_price:<8.2f} {sell_price:<8.2f} {return_rate*100:<7.2f}% "
                  f"{tech_strength:<6} {volume_ratio:<8.1f}")
        
        print(f"{'='*120}")
        print(f"✅ 验证结果: 买卖规则完全符合 '预测上涨当日开盘买入，次日开盘卖出'")
    
    def analyze_best_strategies(self, trading_df):
        """分析最佳策略"""
        print(f"\n🎯 基于正确买卖规则的最佳策略分析:")
        
        # 处理缺失值
        trading_df['技术指标特征'] = trading_df['技术指标特征'].fillna('Unknown')
        trading_df['趋势组合'] = trading_df['趋势组合'].fillna('Unknown')
        trading_df['日内股票标记'] = trading_df['日内股票标记'].fillna('Unknown')
        
        best_strategies = []
        
        # 1. 日内股票标记分析
        print(f"\n  📊 日内股票标记策略:")
        for mark in trading_df['日内股票标记'].unique():
            if pd.notna(mark) and mark != 'Unknown':
                subset = trading_df[trading_df['日内股票标记'] == mark]
                if len(subset) >= 100:  # 至少100笔交易
                    win_rate = subset['是否盈利'].mean()
                    avg_return = subset['收益率'].mean()
                    total_trades = len(subset)
                    total_profit = subset['盈利金额'].sum()
                    
                    if win_rate >= 0.70:  # 胜率≥70%
                        print(f"    标记={mark}: 胜率{win_rate:.3f}({win_rate*100:.1f}%) "
                              f"平均收益{avg_return:.4f}({avg_return*100:.2f}%) "
                              f"交易{total_trades}笔 总盈利{total_profit:.0f}元")
                        
                        best_strategies.append({
                            'strategy': f'日内股票标记={mark}',
                            'win_rate': win_rate,
                            'avg_return': avg_return,
                            'total_trades': total_trades,
                            'total_profit': total_profit,
                            'score': win_rate * avg_return
                        })
        
        # 2. 技术强度+成交量组合
        print(f"\n  🚀 技术强度+成交量组合:")
        tech_values = [85, 100]
        volume_values = [2.0, 2.5, 3.0, 3.5]
        
        for tech in tech_values:
            for vol in volume_values:
                subset = trading_df[(trading_df['技术强度'] == tech) & 
                                  (trading_df['成交量是前一日几倍'] == vol)]
                if len(subset) >= 50:
                    win_rate = subset['是否盈利'].mean()
                    avg_return = subset['收益率'].mean()
                    total_trades = len(subset)
                    total_profit = subset['盈利金额'].sum()
                    
                    if win_rate >= 0.75:
                        print(f"    技术强度={tech}, 成交量={vol}倍: "
                              f"胜率{win_rate:.3f}({win_rate*100:.1f}%) "
                              f"平均收益{avg_return:.4f}({avg_return*100:.2f}%) "
                              f"交易{total_trades}笔 总盈利{total_profit:.0f}元")
                        
                        best_strategies.append({
                            'strategy': f'技术强度={tech}, 成交量={vol}倍',
                            'win_rate': win_rate,
                            'avg_return': avg_return,
                            'total_trades': total_trades,
                            'total_profit': total_profit,
                            'score': win_rate * avg_return
                        })
        
        # 按综合评分排序
        best_strategies.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"\n🏆 最佳策略排名 (基于正确买卖规则):")
        for i, strategy in enumerate(best_strategies[:10], 1):
            print(f"  {i:2d}. {strategy['strategy']}")
            print(f"      胜率: {strategy['win_rate']:.3f} ({strategy['win_rate']*100:.1f}%)")
            print(f"      平均收益率: {strategy['avg_return']:.4f} ({strategy['avg_return']*100:.2f}%)")
            print(f"      交易次数: {strategy['total_trades']}")
            print(f"      总盈利: {strategy['total_profit']:.0f}元")
            print(f"      综合评分: {strategy['score']:.6f}")
            print()
        
        return best_strategies
    
    def save_verification_results(self, trading_df, strategies, output_path):
        """保存验证结果"""
        # 创建详细的交易记录
        detailed_results = trading_df[[
            '股票代码', '买入日期', '卖出日期', 
            '买入价格_当日开盘', '卖出价格_次日开盘', 
            '收益率', '盈利金额', '是否盈利',
            '技术强度', '技术指标特征', '趋势组合', 
            '成交量是前一日几倍', '日内股票标记'
        ]].copy()
        
        # 重命名列
        detailed_results.columns = [
            '股票代码', '买入日期', '卖出日期',
            '买入价格(当日开盘)', '卖出价格(次日开盘)',
            '收益率', '盈利金额', '是否盈利',
            '技术强度', '技术指标特征', '趋势组合',
            '成交量倍数', '日内股票标记'
        ]
        
        # 保存到Excel
        with pd.ExcelWriter(output_path) as writer:
            # 详细交易记录
            detailed_results.to_excel(writer, sheet_name='详细交易记录', index=False)
            
            # 最佳策略
            if strategies:
                strategy_df = pd.DataFrame([{
                    '策略描述': s['strategy'],
                    '胜率': f"{s['win_rate']*100:.1f}%",
                    '平均收益率': f"{s['avg_return']*100:.2f}%",
                    '交易次数': s['total_trades'],
                    '总盈利金额': f"{s['total_profit']:.0f}元",
                    '综合评分': f"{s['score']:.6f}"
                } for s in strategies])
                strategy_df.to_excel(writer, sheet_name='最佳策略', index=False)
        
        print(f"💾 验证结果已保存到: {output_path}")

def main():
    """主函数"""
    start_time = datetime.now()
    
    try:
        # 1. 初始化验证系统
        verification_system = TradingRulesVerification(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily"
        )
        
        # 2. 加载和合并数据
        merged_df = verification_system.load_and_merge_data()
        
        # 3. 模拟交易并验证
        trading_df, verification_df = verification_system.simulate_trading_with_verification(merged_df)
        
        if len(trading_df) == 0:
            print("❌ 没有有效的交易数据")
            return
        
        # 4. 验证交易样本
        verification_system.verify_trading_samples(verification_df)
        
        # 5. 分析最佳策略
        best_strategies = verification_system.analyze_best_strategies(trading_df)
        
        # 6. 保存验证结果
        output_path = f"买卖规则验证_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        verification_system.save_verification_results(trading_df, best_strategies, output_path)
        
        end_time = datetime.now()
        
        print(f"\n" + "="*80)
        print(f"✅ 买卖规则验证完成!")
        print(f"⏱️ 总耗时: {end_time - start_time}")
        print(f"📊 验证了 {len(trading_df):,} 笔实际交易")
        print(f"🎯 发现 {len(best_strategies)} 个高胜率策略")
        print(f"📋 买卖规则: 预测上涨当日开盘买入，次日开盘卖出 ✅")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
