#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
去掉连续10天特征的优化版本
专注于3天和5天的短中期技术强度

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_model import MultiModalStockPredictor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class Remove10DayProcessor:
    """去掉连续10天特征的处理器"""
    
    def __init__(self, tech_strength_path, stock_data_path, sequence_length=20):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.sequence_length = sequence_length
        
        # 标准化器
        self.tech_scaler = StandardScaler()
        self.auxiliary_scaler = StandardScaler()
        self.target_scaler = StandardScaler()
        
        # 标签编码器
        self.label_encoders = {}
    
    def load_and_prepare_data(self):
        """加载并准备数据 - 去掉连续10天特征"""
        print("=" * 80)
        print("🚀 去掉连续10天特征的优化版本")
        print("=" * 80)
        
        # 1. 加载数据
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"技术强度数据: {len(tech_df)} 条记录")
        print(f"股票交易明细: {len(stock_df)} 条记录")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 2. 合并数据
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='left',
            suffixes=('_tech', '_stock')
        )
        
        print(f"合并后数据: {len(merged_df)} 条记录")
        
        # 3. 专注于短中期连续性指标 (去掉10天)
        print(f"\n🔧 短中期连续性指标处理 (去掉10天特征):")
        
        tech_strength_features = ['技术强度']  # 保持原值
        
        # 只使用3天和5天的连续性指标
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300, 'name': '短期'},
            '连续技术强度5天数': {'min': 28, 'max': 500, 'name': '中期'}
        }
        
        for indicator, info in continuous_indicators.items():
            if indicator in merged_df.columns:
                min_val = info['min']
                max_val = info['max']
                name = info['name']
                
                # 覆盖全部数据范围的三等分
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                
                print(f"  {indicator} ({name}): [{min_val}, {max_val}]")
                print(f"    低值段: [{min_val}, {cut1:.1f})")
                print(f"    中值段: [{cut1:.1f}, {cut2:.1f})")
                print(f"    高值段: [{cut2:.1f}, {max_val}]")
                
                # 创建分段
                merged_df[f'{indicator}_分段'] = pd.cut(
                    merged_df[indicator],
                    bins=[min_val - 0.1, cut1, cut2, max_val + 0.1],
                    labels=[1, 2, 3],  # 1:低, 2:中, 3:高
                    include_lowest=True
                ).astype(float)
                
                tech_strength_features.append(f'{indicator}_分段')
                
                # 统计实际分布
                segment_counts = merged_df[f'{indicator}_分段'].value_counts().sort_index()
                total = len(merged_df)
                print(f"    实际分布: 低({segment_counts.get(1.0, 0)}, {segment_counts.get(1.0, 0)/total*100:.1f}%) "
                      f"中({segment_counts.get(2.0, 0)}, {segment_counts.get(2.0, 0)/total*100:.1f}%) "
                      f"高({segment_counts.get(3.0, 0)}, {segment_counts.get(3.0, 0)/total*100:.1f}%)")
        
        # 4. 创建3天和5天的组合特征
        if '连续技术强度3天数_分段' in merged_df.columns and '连续技术强度5天数_分段' in merged_df.columns:
            # 短中期组合特征
            merged_df['短中期组合'] = merged_df['连续技术强度3天数_分段'] * 10 + merged_df['连续技术强度5天数_分段']
            tech_strength_features.append('短中期组合')
            
            # 短中期差异特征
            merged_df['短中期差异'] = merged_df['连续技术强度5天数_分段'] - merged_df['连续技术强度3天数_分段']
            tech_strength_features.append('短中期差异')
            
            print(f"  创建短中期组合特征: 短中期组合, 短中期差异")
        
        # 5. 成交量倍数处理
        if '成交量是前一日几倍' in merged_df.columns:
            tech_strength_features.append('成交量是前一日几倍')
            print(f"  成交量倍数: 保持原值 (7个离散值)")
        
        # 6. 处理分类特征
        print(f"\n🏷️ 处理分类特征:")
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        
        for col in categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                tech_strength_features.append(f'{col}_encoded')
                unique_count = merged_df[f'{col}_encoded'].nunique()
                print(f"  {col}: {unique_count} 个类别")
        
        # 7. 增强辅助特征
        auxiliary_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价',
            '成交量_stock', '成交额', '换手率', '涨跌幅_stock'
        ]
        
        # 价格相关的衍生特征
        if all(col in merged_df.columns for col in ['开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价']):
            # 价格波动率
            merged_df['价格波动率'] = (merged_df['最高价'] - merged_df['最低价']) / merged_df['收盘价_stock']
            auxiliary_features.append('价格波动率')
            
            # 开盘缺口
            merged_df['开盘缺口'] = (merged_df['开盘价'] - merged_df['前收盘价']) / merged_df['前收盘价']
            auxiliary_features.append('开盘缺口')
            
            # 收盘强度 (收盘价在当日价格区间的位置)
            price_range = merged_df['最高价'] - merged_df['最低价']
            price_range = price_range.replace(0, 1e-8)  # 避免除零
            merged_df['收盘强度'] = (merged_df['收盘价_stock'] - merged_df['最低价']) / price_range
            auxiliary_features.append('收盘强度')
            
            print(f"  创建价格衍生特征: 价格波动率, 开盘缺口, 收盘强度")
        
        # 成交量相关特征
        if '成交量_stock' in merged_df.columns and '换手率' in merged_df.columns:
            # 成交量强度
            merged_df['成交量强度'] = merged_df['成交量_stock'] * merged_df['换手率']
            auxiliary_features.append('成交量强度')
            
            print(f"  创建成交量衍生特征: 成交量强度")
        
        # 辅助分类特征
        aux_categorical_features = ['复权状态', '交易状态', '是否ST股']
        for col in aux_categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                auxiliary_features.append(f'{col}_encoded')
        
        # 8. 确保特征列存在并处理缺失值
        available_tech_features = [col for col in tech_strength_features if col in merged_df.columns]
        available_auxiliary_features = [col for col in auxiliary_features if col in merged_df.columns]
        
        for col in available_tech_features + available_auxiliary_features:
            if merged_df[col].dtype in ['float64', 'int64']:
                merged_df[col] = merged_df[col].fillna(merged_df[col].median())
        
        # 9. 准备最终数据
        target_column = '涨跌幅_tech'
        feature_columns = available_tech_features + available_auxiliary_features + ['股票代码', '日期', target_column]
        final_df = merged_df[feature_columns].copy().dropna(subset=[target_column])
        
        print(f"\n📊 最终特征统计:")
        print(f"  技术强度特征: {len(available_tech_features)} 个")
        print(f"  辅助特征: {len(available_auxiliary_features)} 个")
        print(f"  总特征数: {len(available_tech_features) + len(available_auxiliary_features)}")
        print(f"  最终数据: {len(final_df)} 条记录")
        
        print(f"\n📋 技术强度特征列表:")
        for i, feature in enumerate(available_tech_features, 1):
            print(f"  {i:2d}. {feature}")
        
        print(f"\n✅ 去掉连续10天特征，专注于短中期信号")
        
        return final_df, available_tech_features, available_auxiliary_features, target_column
    
    def create_sequences(self, df, tech_features, auxiliary_features, target_column):
        """创建时间序列"""
        print(f"\n⏰ 创建时间序列 (序列长度: {self.sequence_length})...")
        
        X_tech_list = []
        X_auxiliary_list = []
        y_list = []
        
        for _, group in df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            if len(group) < self.sequence_length + 1:
                continue
            
            tech_data = group[tech_features].values
            auxiliary_data = group[auxiliary_features].values
            target_data = group[target_column].values
            
            for i in range(len(group) - self.sequence_length):
                X_tech_list.append(tech_data[i:i+self.sequence_length])
                X_auxiliary_list.append(auxiliary_data[i:i+self.sequence_length])
                y_list.append(target_data[i+self.sequence_length])
        
        X_tech = np.array(X_tech_list)
        X_auxiliary = np.array(X_auxiliary_list)
        y = np.array(y_list)
        
        print(f"  创建序列: {len(X_tech)} 个样本")
        print(f"  技术强度特征形状: {X_tech.shape}")
        print(f"  辅助特征形状: {X_auxiliary.shape}")
        
        return X_tech, X_auxiliary, y
    
    def normalize_data(self, X_tech, X_auxiliary, y, fit_scalers=False):
        """标准化数据"""
        if fit_scalers:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.tech_scaler.fit_transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.fit_transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.tech_scaler.transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.transform(y.reshape(-1, 1)).flatten()
        
        X_tech_scaled = X_tech_scaled.reshape(X_tech.shape)
        X_auxiliary_scaled = X_auxiliary_scaled.reshape(X_auxiliary.shape)
        
        return X_tech_scaled, X_auxiliary_scaled, y_scaled
    
    def inverse_transform_target(self, y_scaled):
        """反标准化目标变量"""
        return self.target_scaler.inverse_transform(y_scaled.reshape(-1, 1)).flatten()

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)
    
    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)
    
    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def remove_10day_test():
    """去掉连续10天特征的测试"""
    try:
        # 1. 初始化处理器
        processor = Remove10DayProcessor(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily",
            sequence_length=20
        )
        
        # 2. 加载和准备数据
        final_df, tech_features, auxiliary_features, target_column = processor.load_and_prepare_data()
        
        # 3. 创建序列
        X_tech, X_auxiliary, y = processor.create_sequences(final_df, tech_features, auxiliary_features, target_column)
        
        # 4. 数据分割
        print(f"\n📊 数据分割...")
        n_samples = len(X_tech)
        train_size = int(n_samples * 0.7)
        val_size = int(n_samples * 0.15)
        
        X_tech_train = X_tech[:train_size]
        X_auxiliary_train = X_auxiliary[:train_size]
        y_train = y[:train_size]
        
        X_tech_val = X_tech[train_size:train_size+val_size]
        X_auxiliary_val = X_auxiliary[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        
        X_tech_test = X_tech[train_size+val_size:]
        X_auxiliary_test = X_auxiliary[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"  训练集: {len(X_tech_train)} 样本")
        print(f"  验证集: {len(X_tech_val)} 样本")
        print(f"  测试集: {len(X_tech_test)} 样本")
        
        # 5. 数据标准化
        print(f"\n🔧 数据标准化...")
        X_tech_train_scaled, X_auxiliary_train_scaled, y_train_scaled = processor.normalize_data(
            X_tech_train, X_auxiliary_train, y_train, fit_scalers=True
        )
        X_tech_val_scaled, X_auxiliary_val_scaled, y_val_scaled = processor.normalize_data(
            X_tech_val, X_auxiliary_val, y_val, fit_scalers=False
        )
        X_tech_test_scaled, X_auxiliary_test_scaled, y_test_scaled = processor.normalize_data(
            X_tech_test, X_auxiliary_test, y_test, fit_scalers=False
        )
        
        # 6. 创建和训练模型
        print(f"\n🏗️ 创建和训练优化模型...")
        model = MultiModalStockPredictor(
            sequence_length=processor.sequence_length,
            price_features=X_auxiliary_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[128, 256, 128],  # 适中的CNN
            lstm_units=[256, 128],        # 适中的LSTM
            attention_heads=16,           # 适中的注意力头
            dropout_rate=0.4              # 适中的dropout
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.0005)
        
        print(f"  模型参数量: {model.model.count_params()}")
        
        # 训练模型
        model.train(
            X_auxiliary_train_scaled, X_tech_train_scaled, y_train_scaled,
            X_auxiliary_val_scaled, X_tech_val_scaled, y_val_scaled,
            epochs=15,
            batch_size=64
        )
        
        # 7. 模型评估
        print(f"\n📈 模型评估...")
        
        y_pred_scaled = model.predict(X_auxiliary_test_scaled, X_tech_test_scaled)
        y_pred = processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = processor.inverse_transform_target(y_test_scaled)
        
        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        
        # 8. 结果展示
        print("\n" + "=" * 80)
        print("📊 去掉连续10天特征的挖掘结果")
        print("=" * 80)
        
        print(f"\n🎯 方向预测准确率:")
        print(f"   方向准确率: {direction_metrics['direction_accuracy']:.4f} ({direction_metrics['direction_accuracy']*100:.2f}%)")
        print(f"   精确率: {direction_metrics['precision']:.4f}")
        print(f"   召回率: {direction_metrics['recall']:.4f}")
        print(f"   F1分数: {direction_metrics['f1_score']:.4f}")
        
        print(f"\n📊 优化效果:")
        print(f"   ✅ 去掉连续10天特征，减少噪声")
        print(f"   ✅ 专注于短中期信号 (3天+5天)")
        print(f"   ✅ 增加短中期组合特征")
        print(f"   ✅ 增强价格和成交量衍生特征")
        
        return True, direction_metrics['direction_accuracy']
        
    except Exception as e:
        print(f"❌ 去掉10天特征测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    start_time = datetime.now()
    success, accuracy = remove_10day_test()
    end_time = datetime.now()
    
    if success:
        print(f"\n⏱️ 总耗时: {end_time - start_time}")
        print(f"\n🎉 去掉10天特征测试完成!")
        print(f"   📍 最终准确率: {accuracy*100:.2f}%")
        
        baseline_accuracy = 64.81
        previous_accuracy = 65.73
        improvement_vs_baseline = (accuracy * 100) - baseline_accuracy
        improvement_vs_previous = (accuracy * 100) - previous_accuracy
        
        print(f"\n📈 准确率对比:")
        print(f"   基础版本: {baseline_accuracy:.2f}%")
        print(f"   包含10天版本: {previous_accuracy:.2f}%")
        print(f"   去掉10天版本: {accuracy*100:.2f}%")
        
        if improvement_vs_baseline > 0:
            print(f"   ✅ 相比基础版本提升: {improvement_vs_baseline:.2f} 个百分点!")
        else:
            print(f"   ⚠️ 相比基础版本下降: {abs(improvement_vs_baseline):.2f} 个百分点")
            
        if improvement_vs_previous > 0:
            print(f"   ✅ 相比包含10天版本提升: {improvement_vs_previous:.2f} 个百分点!")
            print(f"   🎯 证明去掉10天特征是正确的!")
        else:
            print(f"   ⚠️ 相比包含10天版本下降: {abs(improvement_vs_previous):.2f} 个百分点")
            print(f"   🤔 10天特征可能还是有用的")
    else:
        print("❌ 去掉10天特征测试失败")

if __name__ == "__main__":
    main()
