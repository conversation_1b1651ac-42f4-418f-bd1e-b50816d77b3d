#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
集成学习优化 - 多模型融合提升准确率

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_model import MultiModalStockPredictor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
import xgboost as xgb

class EnsembleOptimizationProcessor:
    """集成学习优化处理器"""
    
    def __init__(self, tech_strength_path, stock_data_path):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        
        # 多个序列长度
        self.sequence_lengths = [15, 20, 25]
        
        # 标准化器
        self.scalers = {}
        self.label_encoders = {}
    
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("=" * 80)
        print("🚀 集成学习优化 - 多模型融合")
        print("=" * 80)
        
        # 1. 加载数据
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"技术强度数据: {len(tech_df)} 条记录")
        print(f"股票交易明细: {len(stock_df)} 条记录")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 2. 合并数据
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='left',
            suffixes=('_tech', '_stock')
        )
        
        print(f"合并后数据: {len(merged_df)} 条记录")
        
        # 3. 增强特征工程
        print(f"\n🔧 增强特征工程:")
        
        tech_strength_features = ['技术强度']
        
        # 连续性指标分段 (去掉10天)
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500}
        }
        
        for indicator, info in continuous_indicators.items():
            if indicator in merged_df.columns:
                min_val = info['min']
                max_val = info['max']
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                
                merged_df[f'{indicator}_分段'] = pd.cut(
                    merged_df[indicator],
                    bins=[min_val - 0.1, cut1, cut2, max_val + 0.1],
                    labels=[1, 2, 3],
                    include_lowest=True
                ).astype(float)
                
                tech_strength_features.append(f'{indicator}_分段')
        
        # 技术强度的时序特征
        if '技术强度' in merged_df.columns:
            # 按股票分组计算移动特征
            for stock_code, group in merged_df.groupby('股票代码'):
                group = group.sort_values('日期').reset_index(drop=True)
                indices = group.index
                
                # 技术强度移动平均
                merged_df.loc[indices, '技术强度_MA3'] = group['技术强度'].rolling(3, min_periods=1).mean()
                merged_df.loc[indices, '技术强度_MA5'] = group['技术强度'].rolling(5, min_periods=1).mean()
                
                # 技术强度变化率
                merged_df.loc[indices, '技术强度_变化率'] = group['技术强度'].pct_change().fillna(0)
                
                # 技术强度波动率
                merged_df.loc[indices, '技术强度_波动率'] = group['技术强度'].rolling(5, min_periods=1).std().fillna(0)
            
            tech_strength_features.extend(['技术强度_MA3', '技术强度_MA5', '技术强度_变化率', '技术强度_波动率'])
        
        # 短中期组合特征
        if '连续技术强度3天数_分段' in merged_df.columns and '连续技术强度5天数_分段' in merged_df.columns:
            merged_df['短中期组合'] = merged_df['连续技术强度3天数_分段'] * 10 + merged_df['连续技术强度5天数_分段']
            merged_df['短中期差异'] = merged_df['连续技术强度5天数_分段'] - merged_df['连续技术强度3天数_分段']
            tech_strength_features.extend(['短中期组合', '短中期差异'])
        
        # 成交量特征
        if '成交量是前一日几倍' in merged_df.columns:
            tech_strength_features.append('成交量是前一日几倍')
        
        # 分类特征
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for col in categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                tech_strength_features.append(f'{col}_encoded')
        
        # 4. 增强辅助特征
        auxiliary_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价',
            '成交量_stock', '成交额', '换手率', '涨跌幅_stock'
        ]
        
        # 价格技术指标
        if all(col in merged_df.columns for col in ['开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价']):
            # 按股票分组计算技术指标
            for stock_code, group in merged_df.groupby('股票代码'):
                group = group.sort_values('日期').reset_index(drop=True)
                indices = group.index
                
                # 价格移动平均
                merged_df.loc[indices, '收盘价_MA5'] = group['收盘价_stock'].rolling(5, min_periods=1).mean()
                merged_df.loc[indices, '收盘价_MA10'] = group['收盘价_stock'].rolling(10, min_periods=1).mean()
                
                # RSI指标
                delta = group['收盘价_stock'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(14, min_periods=1).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(14, min_periods=1).mean()
                rs = gain / loss.replace(0, 1e-8)
                merged_df.loc[indices, 'RSI'] = 100 - (100 / (1 + rs))
                
                # 布林带
                ma20 = group['收盘价_stock'].rolling(20, min_periods=1).mean()
                std20 = group['收盘价_stock'].rolling(20, min_periods=1).std()
                merged_df.loc[indices, '布林带位置'] = (group['收盘价_stock'] - ma20) / (2 * std20.replace(0, 1e-8))
            
            auxiliary_features.extend(['收盘价_MA5', '收盘价_MA10', 'RSI', '布林带位置'])
            
            # 价格衍生特征
            merged_df['价格波动率'] = (merged_df['最高价'] - merged_df['最低价']) / merged_df['收盘价_stock']
            merged_df['开盘缺口'] = (merged_df['开盘价'] - merged_df['前收盘价']) / merged_df['前收盘价']
            price_range = merged_df['最高价'] - merged_df['最低价']
            price_range = price_range.replace(0, 1e-8)
            merged_df['收盘强度'] = (merged_df['收盘价_stock'] - merged_df['最低价']) / price_range
            
            auxiliary_features.extend(['价格波动率', '开盘缺口', '收盘强度'])
        
        # 成交量技术指标
        if '成交量_stock' in merged_df.columns:
            # 按股票分组计算成交量指标
            for stock_code, group in merged_df.groupby('股票代码'):
                group = group.sort_values('日期').reset_index(drop=True)
                indices = group.index
                
                # 成交量移动平均
                merged_df.loc[indices, '成交量_MA5'] = group['成交量_stock'].rolling(5, min_periods=1).mean()
                merged_df.loc[indices, '成交量比率'] = group['成交量_stock'] / merged_df.loc[indices, '成交量_MA5']
            
            auxiliary_features.extend(['成交量_MA5', '成交量比率'])
        
        # 辅助分类特征
        aux_categorical_features = ['复权状态', '交易状态', '是否ST股']
        for col in aux_categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                auxiliary_features.append(f'{col}_encoded')
        
        # 5. 确保特征列存在并处理缺失值
        available_tech_features = [col for col in tech_strength_features if col in merged_df.columns]
        available_auxiliary_features = [col for col in auxiliary_features if col in merged_df.columns]
        
        for col in available_tech_features + available_auxiliary_features:
            if merged_df[col].dtype in ['float64', 'int64']:
                merged_df[col] = merged_df[col].fillna(merged_df[col].median())
        
        # 6. 准备最终数据
        target_column = '涨跌幅_tech'
        feature_columns = available_tech_features + available_auxiliary_features + ['股票代码', '日期', target_column]
        final_df = merged_df[feature_columns].copy().dropna(subset=[target_column])
        
        print(f"\n📊 增强特征统计:")
        print(f"  技术强度特征: {len(available_tech_features)} 个")
        print(f"  辅助特征: {len(available_auxiliary_features)} 个")
        print(f"  总特征数: {len(available_tech_features) + len(available_auxiliary_features)}")
        print(f"  最终数据: {len(final_df)} 条记录")
        
        return final_df, available_tech_features, available_auxiliary_features, target_column
    
    def create_multiple_sequences(self, df, tech_features, auxiliary_features, target_column):
        """创建多个不同长度的时间序列"""
        print(f"\n⏰ 创建多个时间序列...")
        
        sequences_data = {}
        
        for seq_len in self.sequence_lengths:
            print(f"  创建序列长度 {seq_len}...")
            
            X_tech_list = []
            X_auxiliary_list = []
            y_list = []
            
            for _, group in df.groupby('股票代码'):
                group = group.sort_values('日期').reset_index(drop=True)
                
                if len(group) < seq_len + 1:
                    continue
                
                tech_data = group[tech_features].values
                auxiliary_data = group[auxiliary_features].values
                target_data = group[target_column].values
                
                for i in range(len(group) - seq_len):
                    X_tech_list.append(tech_data[i:i+seq_len])
                    X_auxiliary_list.append(auxiliary_data[i:i+seq_len])
                    y_list.append(target_data[i+seq_len])
            
            X_tech = np.array(X_tech_list)
            X_auxiliary = np.array(X_auxiliary_list)
            y = np.array(y_list)
            
            sequences_data[seq_len] = {
                'X_tech': X_tech,
                'X_auxiliary': X_auxiliary,
                'y': y
            }
            
            print(f"    序列长度 {seq_len}: {len(X_tech)} 个样本")
        
        return sequences_data
    
    def normalize_data(self, X_tech, X_auxiliary, y, seq_len, fit_scalers=False):
        """标准化数据"""
        scaler_key = f'seq_{seq_len}'
        
        if fit_scalers:
            self.scalers[f'{scaler_key}_tech'] = StandardScaler()
            self.scalers[f'{scaler_key}_auxiliary'] = StandardScaler()
            self.scalers[f'{scaler_key}_target'] = StandardScaler()
            
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.scalers[f'{scaler_key}_tech'].fit_transform(X_tech_reshaped)
            X_auxiliary_scaled = self.scalers[f'{scaler_key}_auxiliary'].fit_transform(X_auxiliary_reshaped)
            y_scaled = self.scalers[f'{scaler_key}_target'].fit_transform(y.reshape(-1, 1)).flatten()
        else:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.scalers[f'{scaler_key}_tech'].transform(X_tech_reshaped)
            X_auxiliary_scaled = self.scalers[f'{scaler_key}_auxiliary'].transform(X_auxiliary_reshaped)
            y_scaled = self.scalers[f'{scaler_key}_target'].transform(y.reshape(-1, 1)).flatten()
        
        X_tech_scaled = X_tech_scaled.reshape(X_tech.shape)
        X_auxiliary_scaled = X_auxiliary_scaled.reshape(X_auxiliary.shape)
        
        return X_tech_scaled, X_auxiliary_scaled, y_scaled
    
    def inverse_transform_target(self, y_scaled, seq_len):
        """反标准化目标变量"""
        scaler_key = f'seq_{seq_len}_target'
        return self.scalers[scaler_key].inverse_transform(y_scaled.reshape(-1, 1)).flatten()

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)
    
    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)
    
    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def ensemble_optimization_test():
    """集成学习优化测试"""
    try:
        # 1. 初始化处理器
        processor = EnsembleOptimizationProcessor(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily"
        )
        
        # 2. 加载和准备数据
        final_df, tech_features, auxiliary_features, target_column = processor.load_and_prepare_data()
        
        # 3. 创建多个序列
        sequences_data = processor.create_multiple_sequences(final_df, tech_features, auxiliary_features, target_column)
        
        # 4. 训练多个模型
        print(f"\n🏗️ 训练多个模型...")
        
        models = {}
        predictions = {}
        
        # 找到最小的样本数量用于对齐
        min_samples = min([len(data['y']) for data in sequences_data.values()])
        
        for seq_len in processor.sequence_lengths:
            print(f"\n训练序列长度 {seq_len} 的模型...")
            
            data = sequences_data[seq_len]
            X_tech = data['X_tech'][:min_samples]
            X_auxiliary = data['X_auxiliary'][:min_samples]
            y = data['y'][:min_samples]
            
            # 数据分割
            train_size = int(min_samples * 0.8)
            
            X_tech_train = X_tech[:train_size]
            X_auxiliary_train = X_auxiliary[:train_size]
            y_train = y[:train_size]
            
            X_tech_test = X_tech[train_size:]
            X_auxiliary_test = X_auxiliary[train_size:]
            y_test = y[train_size:]
            
            # 数据标准化
            X_tech_train_scaled, X_auxiliary_train_scaled, y_train_scaled = processor.normalize_data(
                X_tech_train, X_auxiliary_train, y_train, seq_len, fit_scalers=True
            )
            X_tech_test_scaled, X_auxiliary_test_scaled, y_test_scaled = processor.normalize_data(
                X_tech_test, X_auxiliary_test, y_test, seq_len, fit_scalers=False
            )
            
            # 创建和训练模型
            model = MultiModalStockPredictor(
                sequence_length=seq_len,
                price_features=X_auxiliary_train.shape[-1],
                tech_features=X_tech_train.shape[-1],
                cnn_filters=[128, 256, 128],
                lstm_units=[256, 128],
                attention_heads=16,
                dropout_rate=0.4
            )
            
            model.build_model()
            model.compile_model(learning_rate=0.0005)
            
            # 快速训练
            model.train(
                X_auxiliary_train_scaled, X_tech_train_scaled, y_train_scaled,
                epochs=10,  # 减少epoch以节省时间
                batch_size=64
            )
            
            # 预测
            y_pred_scaled = model.predict(X_auxiliary_test_scaled, X_tech_test_scaled)
            y_pred = processor.inverse_transform_target(y_pred_scaled.flatten(), seq_len)
            y_test_original = processor.inverse_transform_target(y_test_scaled, seq_len)
            
            models[seq_len] = model
            predictions[seq_len] = {
                'y_pred': y_pred,
                'y_test': y_test_original
            }
            
            # 单个模型准确率
            metrics = calculate_direction_accuracy(y_test_original, y_pred)
            print(f"  序列长度 {seq_len} 准确率: {metrics['direction_accuracy']:.4f} ({metrics['direction_accuracy']*100:.2f}%)")
        
        # 5. 集成预测
        print(f"\n🔗 集成多个模型预测...")
        
        # 简单平均集成
        ensemble_pred = np.mean([predictions[seq_len]['y_pred'] for seq_len in processor.sequence_lengths], axis=0)
        y_test_ensemble = predictions[processor.sequence_lengths[0]]['y_test']  # 所有模型的测试集相同
        
        # 加权平均集成 (根据单个模型性能加权)
        weights = []
        for seq_len in processor.sequence_lengths:
            metrics = calculate_direction_accuracy(
                predictions[seq_len]['y_test'], 
                predictions[seq_len]['y_pred']
            )
            weights.append(metrics['direction_accuracy'])
        
        weights = np.array(weights)
        weights = weights / weights.sum()  # 归一化权重
        
        weighted_ensemble_pred = np.average(
            [predictions[seq_len]['y_pred'] for seq_len in processor.sequence_lengths], 
            axis=0, 
            weights=weights
        )
        
        # 6. 评估集成结果
        simple_ensemble_metrics = calculate_direction_accuracy(y_test_ensemble, ensemble_pred)
        weighted_ensemble_metrics = calculate_direction_accuracy(y_test_ensemble, weighted_ensemble_pred)
        
        # 7. 结果展示
        print("\n" + "=" * 80)
        print("📊 集成学习优化结果")
        print("=" * 80)
        
        print(f"\n🎯 单个模型结果:")
        for seq_len in processor.sequence_lengths:
            metrics = calculate_direction_accuracy(
                predictions[seq_len]['y_test'], 
                predictions[seq_len]['y_pred']
            )
            print(f"   序列长度 {seq_len}: {metrics['direction_accuracy']:.4f} ({metrics['direction_accuracy']*100:.2f}%)")
        
        print(f"\n🔗 集成模型结果:")
        print(f"   简单平均集成: {simple_ensemble_metrics['direction_accuracy']:.4f} ({simple_ensemble_metrics['direction_accuracy']*100:.2f}%)")
        print(f"   加权平均集成: {weighted_ensemble_metrics['direction_accuracy']:.4f} ({weighted_ensemble_metrics['direction_accuracy']*100:.2f}%)")
        
        print(f"\n📊 集成权重:")
        for i, seq_len in enumerate(processor.sequence_lengths):
            print(f"   序列长度 {seq_len}: {weights[i]:.3f}")
        
        best_accuracy = max(
            simple_ensemble_metrics['direction_accuracy'],
            weighted_ensemble_metrics['direction_accuracy']
        )
        
        return True, best_accuracy
        
    except Exception as e:
        print(f"❌ 集成优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    start_time = datetime.now()
    success, accuracy = ensemble_optimization_test()
    end_time = datetime.now()
    
    if success:
        print(f"\n⏱️ 总耗时: {end_time - start_time}")
        print(f"\n🎉 集成学习优化完成!")
        print(f"   📍 最终准确率: {accuracy*100:.2f}%")
        
        baseline_accuracy = 64.81
        previous_accuracy = 66.62
        improvement_vs_baseline = (accuracy * 100) - baseline_accuracy
        improvement_vs_previous = (accuracy * 100) - previous_accuracy
        
        print(f"\n📈 准确率对比:")
        print(f"   基础版本: {baseline_accuracy:.2f}%")
        print(f"   去掉10天版本: {previous_accuracy:.2f}%")
        print(f"   集成学习版本: {accuracy*100:.2f}%")
        
        if improvement_vs_baseline > 0:
            print(f"   ✅ 相比基础版本提升: {improvement_vs_baseline:.2f} 个百分点!")
        
        if improvement_vs_previous > 0:
            print(f"   ✅ 相比上次提升: {improvement_vs_previous:.2f} 个百分点!")
            print(f"   🚀 集成学习带来额外提升!")
        else:
            print(f"   ⚠️ 相比上次下降: {abs(improvement_vs_previous):.2f} 个百分点")
            print(f"   💡 可能需要更多模型或更好的集成策略")
    else:
        print("❌ 集成学习优化失败")

if __name__ == "__main__":
    main()
