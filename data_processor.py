import pandas as pd
import numpy as np
import os
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import glob
from typing import Tuple, List, Dict
import warnings
warnings.filterwarnings('ignore')

class StockDataProcessor:
    """股票数据预处理类"""
    
    def __init__(self, data_path: str = "stock_data/daily", sequence_length: int = 30):
        """
        初始化数据处理器
        
        Args:
            data_path: 数据文件路径
            sequence_length: 时间序列长度
        """
        self.data_path = data_path
        self.sequence_length = sequence_length
        self.scaler_features = StandardScaler()
        self.scaler_target = MinMaxScaler()
        self.feature_columns = []
        self.target_column = '涨跌幅'
        
    def load_all_data(self) -> pd.DataFrame:
        """
        加载所有Excel文件数据
        
        Returns:
            合并后的DataFrame
        """
        print("正在加载数据文件...")
        excel_files = glob.glob(os.path.join(self.data_path, "*.xlsx"))
        excel_files.sort()  # 按日期排序
        
        all_data = []
        for file in excel_files:
            try:
                df = pd.read_excel(file)
                all_data.append(df)
                print(f"已加载: {os.path.basename(file)}")
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
                
        if not all_data:
            raise ValueError("没有找到有效的数据文件")
            
        combined_data = pd.concat(all_data, ignore_index=True)
        print(f"总共加载了 {len(combined_data)} 条记录")
        
        return combined_data
    
    def create_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建技术指标特征
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            添加技术指标后的DataFrame
        """
        print("正在创建技术指标特征...")
        
        # 按股票代码分组处理
        processed_groups = []
        
        for stock_code, group in df.groupby('证券代码'):
            group = group.sort_values('日期').copy()
            
            # 基础价格特征
            group['价格变化率'] = group['收盘价'].pct_change()
            group['高低价差'] = (group['最高价'] - group['最低价']) / group['收盘价']
            group['开收价差'] = (group['收盘价'] - group['开盘价']) / group['开盘价']
            
            # 移动平均线
            for window in [5, 10, 20]:
                group[f'MA_{window}'] = group['收盘价'].rolling(window=window).mean()
                group[f'MA_{window}_ratio'] = group['收盘价'] / group[f'MA_{window}']
            
            # 波动率指标
            group['volatility_5'] = group['价格变化率'].rolling(window=5).std()
            group['volatility_10'] = group['价格变化率'].rolling(window=10).std()
            
            # RSI指标
            delta = group['收盘价'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            group['RSI'] = 100 - (100 / (1 + rs))
            
            # MACD指标
            exp1 = group['收盘价'].ewm(span=12).mean()
            exp2 = group['收盘价'].ewm(span=26).mean()
            group['MACD'] = exp1 - exp2
            group['MACD_signal'] = group['MACD'].ewm(span=9).mean()
            group['MACD_histogram'] = group['MACD'] - group['MACD_signal']
            
            # 成交量指标
            group['volume_ma_5'] = group['成交量'].rolling(window=5).mean()
            group['volume_ratio'] = group['成交量'] / group['volume_ma_5']
            
            # 布林带
            group['BB_middle'] = group['收盘价'].rolling(window=20).mean()
            bb_std = group['收盘价'].rolling(window=20).std()
            group['BB_upper'] = group['BB_middle'] + (bb_std * 2)
            group['BB_lower'] = group['BB_middle'] - (bb_std * 2)
            group['BB_position'] = (group['收盘价'] - group['BB_lower']) / (group['BB_upper'] - group['BB_lower'])
            
            processed_groups.append(group)
        
        result_df = pd.concat(processed_groups, ignore_index=True)
        
        # 删除包含NaN的行
        result_df = result_df.dropna()
        
        print(f"技术指标创建完成，剩余 {len(result_df)} 条记录")
        return result_df
    
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        准备特征列
        
        Args:
            df: 包含技术指标的DataFrame
            
        Returns:
            准备好的特征DataFrame
        """
        # 定义特征列
        self.feature_columns = [
            '开盘价', '最高价', '最低价', '收盘价', '成交量', '成交额', '换手率',
            '价格变化率', '高低价差', '开收价差',
            'MA_5', 'MA_10', 'MA_20', 'MA_5_ratio', 'MA_10_ratio', 'MA_20_ratio',
            'volatility_5', 'volatility_10', 'RSI',
            'MACD', 'MACD_signal', 'MACD_histogram',
            'volume_ma_5', 'volume_ratio',
            'BB_middle', 'BB_upper', 'BB_lower', 'BB_position'
        ]
        
        # 确保所有特征列都存在
        available_features = [col for col in self.feature_columns if col in df.columns]
        self.feature_columns = available_features
        
        print(f"使用的特征列: {len(self.feature_columns)} 个")
        return df[self.feature_columns + [self.target_column, '证券代码', '日期']]
    
    def create_sequences(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建时间序列数据
        
        Args:
            df: 特征DataFrame
            
        Returns:
            X: 特征序列 (samples, sequence_length, features)
            y: 目标值 (samples,)
        """
        print("正在创建时间序列...")
        
        X, y = [], []
        
        # 按股票代码分组创建序列
        for stock_code, group in df.groupby('证券代码'):
            group = group.sort_values('日期')
            
            if len(group) < self.sequence_length + 1:
                continue
                
            features = group[self.feature_columns].values
            targets = group[self.target_column].values
            
            for i in range(len(features) - self.sequence_length):
                X.append(features[i:i + self.sequence_length])
                y.append(targets[i + self.sequence_length])
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"创建了 {len(X)} 个序列样本")
        print(f"特征形状: {X.shape}")
        print(f"目标形状: {y.shape}")
        
        return X, y
    
    def normalize_data(self, X: np.ndarray, y: np.ndarray, fit_scalers: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """
        标准化数据
        
        Args:
            X: 特征数据
            y: 目标数据
            fit_scalers: 是否拟合缩放器
            
        Returns:
            标准化后的X和y
        """
        print("正在标准化数据...")
        
        # 重塑X以便标准化
        original_shape = X.shape
        X_reshaped = X.reshape(-1, X.shape[-1])
        
        if fit_scalers:
            X_scaled = self.scaler_features.fit_transform(X_reshaped)
            y_scaled = self.scaler_target.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            X_scaled = self.scaler_features.transform(X_reshaped)
            y_scaled = self.scaler_target.transform(y.reshape(-1, 1)).flatten()
        
        X_scaled = X_scaled.reshape(original_shape)
        
        return X_scaled, y_scaled
    
    def process_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        完整的数据处理流程
        
        Returns:
            X_train, X_test, y_train, y_test
        """
        # 加载数据
        df = self.load_all_data()
        
        # 创建技术指标
        df = self.create_technical_features(df)
        
        # 准备特征
        df = self.prepare_features(df)
        
        # 创建序列
        X, y = self.create_sequences(df)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=False
        )
        
        # 标准化
        X_train, y_train = self.normalize_data(X_train, y_train, fit_scalers=True)
        X_test, y_test = self.normalize_data(X_test, y_test, fit_scalers=False)
        
        print("数据处理完成!")
        print(f"训练集: X_train {X_train.shape}, y_train {y_train.shape}")
        print(f"测试集: X_test {X_test.shape}, y_test {y_test.shape}")
        
        return X_train, X_test, y_train, y_test
    
    def inverse_transform_target(self, y_scaled: np.ndarray) -> np.ndarray:
        """
        反标准化目标值
        
        Args:
            y_scaled: 标准化后的目标值
            
        Returns:
            原始尺度的目标值
        """
        return self.scaler_target.inverse_transform(y_scaled.reshape(-1, 1)).flatten()
