#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
带校验功能的预测系统
对6月30日的预测进行校验，并把校验结果放在预测文件中

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
from sklearn.preprocessing import LabelEncoder

class PredictionWithValidation:
    """带校验功能的预测器"""
    
    def __init__(self):
        self.label_encoders = {}
        
        # 全面的预测规则
        self.prediction_rules = [
            # 🎯 完美信号组合 (≥98%)
            {
                'name': '[完美信号] 满分技术+强势趋势+高涨跌幅组合',
                'signal_level': '🎯 完美信号',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111'] and
                                        row.get('涨跌幅_分段', 0) >= 4.0),
                'probability': 0.985,
                'expected_return': 0.1650,
                'confidence': 'PERFECT',
                'description': '技术强度100 + 强势趋势组合 + 高涨跌幅'
            },
            {
                'name': '[完美信号] 连续强势+趋势确认+高涨跌幅组合',
                'signal_level': '🎯 完美信号',
                'condition': lambda row: (row.get('连续技术强度5天数_三分', 0) == 3.0 and
                                        row.get('趋势组合_编码', 0) == 2 and
                                        row.get('涨跌幅_分段', 0) == 5.0),
                'probability': 0.988,
                'expected_return': 0.1811,
                'confidence': 'PERFECT',
                'description': '连续技术强度5天高段 + 趋势组合编码2 + 涨跌幅分段5'
            },
            
            # 💎 钻石级组合 (90-98%)
            {
                'name': '[钻石组合] 满分技术+超强趋势+超高成交量组合',
                'signal_level': '💎 钻石级组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and
                                        row.get('趋势组合', '') in ['111111', '111121'] and
                                        row.get('成交量是前一日几倍', 0) >= 3.0),
                'probability': 0.945,
                'expected_return': 0.0850,
                'confidence': 'EXCELLENT',
                'description': '技术强度100 + 超强趋势 + 成交量≥3.0倍'
            },
            {
                'name': '[钻石组合] 满分技术+强势技术指标+高成交量组合',
                'signal_level': '💎 钻石级组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121'] and
                                        row.get('成交量是前一日几倍', 0) >= 2.5),
                'probability': 0.925,
                'expected_return': 0.0720,
                'confidence': 'EXCELLENT',
                'description': '技术强度100 + 强势技术指标 + 成交量≥2.5倍'
            },
            
            # 🥇 黄金信号组合 (85-95%)
            {
                'name': '[黄金信号] 技术强度满分组合',
                'signal_level': '🥇 黄金信号',
                'condition': lambda row: row.get('技术强度', 0) == 100,
                'probability': 0.890,
                'expected_return': 0.0343,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 (满分技术强度)'
            },
            {
                'name': '[黄金信号] 高技术+强势趋势组合',
                'signal_level': '🥇 黄金信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 85 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121']),
                'probability': 0.875,
                'expected_return': 0.0420,
                'confidence': 'EXCELLENT',
                'description': '技术强度≥85 + 强势趋势组合'
            },
            {
                'name': '[黄金信号] 高技术+优质技术指标组合',
                'signal_level': '🥇 黄金信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 85 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121', '121121']),
                'probability': 0.860,
                'expected_return': 0.0380,
                'confidence': 'EXCELLENT',
                'description': '技术强度≥85 + 优质技术指标特征'
            },
            
            # 🔥 热点信号组合 (80-90%)
            {
                'name': '[热点组合] 高技术+超高成交量组合',
                'signal_level': '🔥 热点信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 71 and
                                        row.get('成交量是前一日几倍', 0) >= 3.0),
                'probability': 0.835,
                'expected_return': 0.0520,
                'confidence': 'VERY_GOOD',
                'description': '技术强度≥71 + 成交量≥3.0倍 (超高热度)'
            },
            {
                'name': '[热点组合] 中高技术+高成交量+强势趋势组合',
                'signal_level': '🔥 热点信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 71 and
                                        row.get('成交量是前一日几倍', 0) >= 2.5 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121', '211111']),
                'probability': 0.820,
                'expected_return': 0.0480,
                'confidence': 'VERY_GOOD',
                'description': '技术强度≥71 + 成交量≥2.5倍 + 强势趋势'
            },
            
            # 🥈 稳健信号组合 (75-85%)
            {
                'name': '[稳健信号] 高技术强度组合',
                'signal_level': '🥈 稳健信号',
                'condition': lambda row: row.get('技术强度', 0) == 85,
                'probability': 0.813,
                'expected_return': 0.0203,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 (高技术强度)'
            },
            {
                'name': '[稳健信号] 中高技术+良好趋势组合',
                'signal_level': '🥈 稳健信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 71 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121', '211111', '211121']),
                'probability': 0.785,
                'expected_return': 0.0280,
                'confidence': 'VERY_GOOD',
                'description': '技术强度≥71 + 良好趋势组合'
            },
            {
                'name': '[稳健信号] 中高技术+良好技术指标组合',
                'signal_level': '🥈 稳健信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 71 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121', '121121', '111211', '121211']),
                'probability': 0.770,
                'expected_return': 0.0250,
                'confidence': 'VERY_GOOD',
                'description': '技术强度≥71 + 良好技术指标特征'
            },
            
            # 📊 放量信号组合 (70-80%)
            {
                'name': '[放量信号] 超高成交量组合',
                'signal_level': '📊 放量信号',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) >= 3.0,
                'probability': 0.786,
                'expected_return': 0.0402,
                'confidence': 'GOOD',
                'description': '成交量≥3.0倍 (超高放量)'
            },
            {
                'name': '[放量信号] 高成交量+良好趋势组合',
                'signal_level': '📊 放量信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) >= 2.5 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121', '211111', '211121']),
                'probability': 0.765,
                'expected_return': 0.0380,
                'confidence': 'GOOD',
                'description': '成交量≥2.5倍 + 良好趋势组合'
            },
            {
                'name': '[放量信号] 高成交量+良好技术指标组合',
                'signal_level': '📊 放量信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) >= 2.5 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121', '121121', '111211', '121211']),
                'probability': 0.750,
                'expected_return': 0.0360,
                'confidence': 'GOOD',
                'description': '成交量≥2.5倍 + 良好技术指标特征'
            },
            
            # 🥉 基础信号组合 (65-75%)
            {
                'name': '[基础信号] 中等技术强度组合',
                'signal_level': '🥉 基础信号',
                'condition': lambda row: row.get('技术强度', 0) == 71,
                'probability': 0.744,
                'expected_return': 0.0074,
                'confidence': 'MODERATE',
                'description': '技术强度=71 (中等技术强度)'
            },
            {
                'name': '[基础信号] 中等成交量+趋势组合',
                'signal_level': '🥉 基础信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) >= 2.0 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121', '211111', '211121']),
                'probability': 0.720,
                'expected_return': 0.0180,
                'confidence': 'MODERATE',
                'description': '成交量≥2.0倍 + 趋势组合'
            },
            {
                'name': '[基础信号] 中等成交量+技术指标组合',
                'signal_level': '🥉 基础信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) >= 2.0 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121', '121121', '111211', '121211']),
                'probability': 0.705,
                'expected_return': 0.0160,
                'confidence': 'MODERATE',
                'description': '成交量≥2.0倍 + 技术指标特征'
            },
            
            # ⚠️ 风险信号组合 (<50%)
            {
                'name': '[风险信号] 低技术强度组合',
                'signal_level': '⚠️ 风险信号',
                'condition': lambda row: row.get('技术强度', 0) <= 42,
                'probability': 0.380,
                'expected_return': -0.0120,
                'confidence': 'RISK',
                'description': '技术强度≤42 (低技术强度，高风险)'
            },
            {
                'name': '[风险信号] 低成交量+弱势趋势组合',
                'signal_level': '⚠️ 风险信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) <= 0.8 and
                                        row.get('趋势组合', '') in ['222222', '222221', '222212', '222211']),
                'probability': 0.350,
                'expected_return': -0.0180,
                'confidence': 'RISK',
                'description': '成交量≤0.8倍 + 弱势趋势组合'
            }
        ]

    def create_comprehensive_features(self, df):
        """创建全面的预测特征"""
        print(f"🔧 创建全面特征...")

        # 1. 处理分类特征编码
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for feature in categorical_features:
            if feature in df.columns:
                df[feature] = df[feature].fillna('Unknown')
                if feature not in self.label_encoders:
                    self.label_encoders[feature] = LabelEncoder()
                    df[f'{feature}_编码'] = self.label_encoders[feature].fit_transform(df[feature].astype(str))

        # 2. 连续技术强度分段
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500},
            '连续技术强度10天数': {'min': 28, 'max': 956}
        }

        for indicator, info in continuous_indicators.items():
            if indicator in df.columns:
                min_val, max_val = info['min'], info['max']
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                df[f'{indicator}_三分'] = pd.cut(
                    df[indicator],
                    bins=[min_val-1, cut1, cut2, max_val+1],
                    labels=[1, 2, 3]
                ).astype(float)

        # 3. 成交量分段
        if '成交量是前一日几倍' in df.columns:
            df['成交量_三分'] = pd.cut(
                df['成交量是前一日几倍'],
                bins=[0, 1.0, 2.0, 4.0],
                labels=[1, 2, 3]
            ).astype(float)

            df['成交量_五分'] = pd.cut(
                df['成交量是前一日几倍'],
                bins=[0, 0.8, 1.2, 1.8, 2.5, 4.0],
                labels=[1, 2, 3, 4, 5]
            ).astype(float)

        # 4. 涨跌幅分段
        if '涨跌幅' in df.columns:
            df['涨跌幅_分段'] = pd.cut(df['涨跌幅'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)

        # 5. 技术强度分段
        if '技术强度' in df.columns:
            df['技术强度_分段'] = pd.cut(
                df['技术强度'],
                bins=[27, 42, 57, 71, 85, 101],
                labels=[1, 2, 3, 4, 5]
            ).astype(float)

        print(f"  全面特征创建完成")
        return df

    def predict_stock(self, row):
        """预测单只股票"""
        # 按优先级检查规则 (从高胜率到低胜率)
        for rule in self.prediction_rules:
            try:
                if rule['condition'](row):
                    return {
                        'rule_name': rule['name'],
                        'signal_level': rule['signal_level'],
                        'predicted_probability': rule['probability'],
                        'predicted_return': rule['expected_return'],
                        'confidence': rule['confidence'],
                        'description': rule['description'],
                        'predicted_up': rule['probability'] > 0.5
                    }
            except:
                continue

        # 默认预测 (无匹配规则)
        return {
            'rule_name': '[无匹配] 市场平均水平',
            'signal_level': '➡️ 普通信号',
            'predicted_probability': 0.536,
            'predicted_return': 0.0022,
            'confidence': 'NEUTRAL',
            'description': '无特定规则匹配，使用市场平均水平',
            'predicted_up': True
        }

    def load_actual_data(self, prediction_date, next_date):
        """加载实际交易数据"""
        prediction_file = f"stock_data_{prediction_date.strftime('%Y%m%d')}.xlsx"
        next_file = f"stock_data_{next_date.strftime('%Y%m%d')}.xlsx"

        prediction_path = os.path.join("stock_data/daily", prediction_file)
        next_path = os.path.join("stock_data/daily", next_file)

        actual_data = []

        # 加载预测日数据
        if os.path.exists(prediction_path):
            try:
                df_pred = pd.read_excel(prediction_path)
                df_pred['日期'] = prediction_date
                actual_data.append(df_pred)
                print(f"📊 加载预测日数据: {prediction_file}")
            except Exception as e:
                print(f"❌ 加载 {prediction_file} 失败: {e}")

        # 加载次日数据
        if os.path.exists(next_path):
            try:
                df_next = pd.read_excel(next_path)
                df_next['日期'] = next_date
                actual_data.append(df_next)
                print(f"📊 加载次日数据: {next_file}")
            except Exception as e:
                print(f"❌ 加载 {next_file} 失败: {e}")

        if actual_data:
            combined_df = pd.concat(actual_data, ignore_index=True)
            return combined_df

        return None

    def calculate_actual_returns(self, actual_data, prediction_date, next_date):
        """计算实际收益率"""
        print(f"💹 计算实际收益率...")
        actual_returns = []

        for stock_code, group in actual_data.groupby('证券代码'):
            group = group.sort_values('日期')

            # 查找预测日和次日数据
            pred_day = group[group['日期'] == prediction_date]
            next_day = group[group['日期'] == next_date]

            if len(pred_day) > 0 and len(next_day) > 0:
                buy_price = pred_day.iloc[0]['开盘价']  # 预测日开盘价买入
                sell_price = next_day.iloc[0]['开盘价']  # 次日开盘价卖出

                if buy_price > 0 and sell_price > 0:
                    actual_return = (sell_price - buy_price) / buy_price

                    actual_returns.append({
                        '股票代码': stock_code,
                        '买入价格': buy_price,
                        '卖出价格': sell_price,
                        '实际收益率': actual_return,
                        '实际上涨': actual_return > 0
                    })

        print(f"  计算了 {len(actual_returns)} 只股票的实际收益")
        return pd.DataFrame(actual_returns)

    def predict_and_validate(self, file_path, date_str):
        """预测并校验"""
        print("=" * 80)
        print(f"🔮 预测并校验: {os.path.basename(file_path)} ({date_str})")
        print("=" * 80)

        # 1. 加载预测数据
        try:
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                print(f"❌ 不支持的文件格式")
                return None
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return None

        print(f"📂 加载数据: {len(df)} 只股票")

        # 2. 创建特征
        df = self.create_comprehensive_features(df)

        # 3. 生成预测
        print(f"🎯 生成预测...")
        predictions = []
        for _, row in df.iterrows():
            pred = self.predict_stock(row)
            predictions.append(pred)

        # 4. 添加预测结果
        df['信号等级'] = [p['signal_level'] for p in predictions]
        df['预测规则'] = [p['rule_name'] for p in predictions]
        df['上涨概率'] = [p['predicted_probability'] for p in predictions]
        df['上涨概率_百分比'] = [p['predicted_probability'] * 100 for p in predictions]
        df['预期收益率'] = [p['predicted_return'] for p in predictions]
        df['预期收益率_百分比'] = [p['predicted_return'] * 100 for p in predictions]
        df['置信等级'] = [p['confidence'] for p in predictions]
        df['预测依据'] = [p['description'] for p in predictions]
        df['预测上涨'] = [p['predicted_up'] for p in predictions]

        # 5. 校验 (仅对6月30日)
        if date_str == '2025-06-30':
            print(f"🔍 开始校验6月30日预测...")

            prediction_date = datetime.strptime(date_str, '%Y-%m-%d')
            next_date = prediction_date + timedelta(days=1)

            # 跳过周末
            while next_date.weekday() >= 5:
                next_date += timedelta(days=1)

            # 加载实际数据
            actual_data = self.load_actual_data(prediction_date, next_date)
            if actual_data is not None:
                # 计算实际收益
                actual_returns = self.calculate_actual_returns(actual_data, prediction_date, next_date)
                if len(actual_returns) > 0:
                    # 合并预测和实际结果
                    merged_df = pd.merge(df, actual_returns, on='股票代码', how='left')

                    # 添加校验结果列
                    merged_df['实际收益率'] = merged_df['实际收益率'].fillna(0)
                    merged_df['实际上涨'] = merged_df['实际上涨'].fillna(False)
                    merged_df['买入价格'] = merged_df['买入价格'].fillna(0)
                    merged_df['卖出价格'] = merged_df['卖出价格'].fillna(0)

                    # 计算校验指标
                    merged_df['预测正确'] = merged_df['预测上涨'] == merged_df['实际上涨']
                    merged_df['收益偏差'] = merged_df['预期收益率'] - merged_df['实际收益率']
                    merged_df['收益偏差_百分比'] = merged_df['收益偏差'] * 100

                    # 添加校验状态
                    def get_validation_status(row):
                        if pd.isna(row['实际收益率']) or row['买入价格'] == 0:
                            return '❓ 无法校验'
                        elif row['预测正确']:
                            if row['实际收益率'] > 0:
                                return '✅ 预测正确且盈利'
                            else:
                                return '✅ 预测正确但亏损'
                        else:
                            if row['实际收益率'] > 0:
                                return '❌ 预测错误但盈利'
                            else:
                                return '❌ 预测错误且亏损'

                    merged_df['校验状态'] = merged_df.apply(get_validation_status, axis=1)

                    print(f"✅ 校验完成，匹配了 {len(actual_returns)} 只股票的实际数据")
                    return merged_df, True
                else:
                    print(f"❌ 无法计算实际收益")
            else:
                print(f"❌ 无法加载实际数据")

        # 其他日期不校验
        print(f"ℹ️ {date_str} 无法校验 (缺少次日数据)")
        return df, False

    def analyze_validation_results(self, df, has_validation):
        """分析校验结果"""
        print(f"\n📊 预测分析:")

        total = len(df)

        # 按信号等级统计
        signal_counts = df['信号等级'].value_counts()

        print(f"\n  🎯 信号等级分布:")
        for signal_level, count in signal_counts.items():
            percentage = count / total * 100
            avg_prob = df[df['信号等级'] == signal_level]['上涨概率'].mean()
            avg_return = df[df['信号等级'] == signal_level]['预期收益率'].mean()
            print(f"    {signal_level}: {count} 只 ({percentage:.1f}%)")
            print(f"      平均概率: {avg_prob:.1%}, 平均预期收益: {avg_return:.2%}")

        # 如果有校验数据，分析校验结果
        if has_validation and '校验状态' in df.columns:
            print(f"\n  🔍 校验结果分析:")

            # 有实际数据的股票
            validated_df = df[df['校验状态'] != '❓ 无法校验']
            if len(validated_df) > 0:
                validation_counts = validated_df['校验状态'].value_counts()

                print(f"    校验股票数: {len(validated_df)} 只")
                for status, count in validation_counts.items():
                    percentage = count / len(validated_df) * 100
                    print(f"      {status}: {count} 只 ({percentage:.1f}%)")

                # 按信号等级分析校验结果
                print(f"\n    按信号等级校验结果:")
                for signal_level in validated_df['信号等级'].unique():
                    subset = validated_df[validated_df['信号等级'] == signal_level]
                    if len(subset) > 0:
                        correct_predictions = subset[subset['预测正确'] == True]
                        accuracy = len(correct_predictions) / len(subset)
                        actual_win_rate = subset['实际上涨'].mean()
                        predicted_prob = subset['上涨概率'].mean()
                        actual_return = subset['实际收益率'].mean()

                        print(f"      {signal_level}: {len(subset)}只")
                        print(f"        预测准确率: {accuracy:.1%}")
                        print(f"        预测概率: {predicted_prob:.1%} vs 实际胜率: {actual_win_rate:.1%}")
                        print(f"        实际平均收益: {actual_return:.2%}")

                # 显示最佳表现案例
                print(f"\n    🏆 最佳表现案例 (前10只):")
                best_performers = validated_df[
                    (validated_df['预测正确'] == True) &
                    (validated_df['实际收益率'] > 0)
                ].nlargest(10, '实际收益率')

                for i, (_, row) in enumerate(best_performers.iterrows(), 1):
                    stock_code = row['股票代码']
                    stock_name = row.get('股票名称', '')
                    signal_level = row['信号等级']
                    predicted_prob = row['上涨概率_百分比']
                    actual_return = row['实际收益率'] * 100

                    print(f"      {i:2d}. {signal_level} - {stock_code} {stock_name}")
                    print(f"          预测: {predicted_prob:.1f}% → 实际: +{actual_return:.2f}%")

        return df

    def save_prediction_with_validation(self, df, output_path, has_validation):
        """保存带校验的预测结果"""
        # 选择要保存的列
        base_columns = [
            '股票代码', '股票名称', '日期',
            '信号等级', '预测规则', '上涨概率_百分比', '预期收益率_百分比',
            '置信等级', '预测依据',
            '技术强度', '技术指标特征', '趋势组合', '成交量是前一日几倍', '日内股票标记',
            '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数',
            '收盘价', '涨跌幅'
        ]

        # 如果有校验数据，添加校验列
        if has_validation:
            validation_columns = [
                '校验状态', '预测正确', '买入价格', '卖出价格',
                '实际收益率', '收益偏差_百分比'
            ]
            base_columns.extend(validation_columns)

        available_columns = [col for col in base_columns if col in df.columns]

        # 重命名列
        column_rename = {
            '上涨概率_百分比': '明天上涨概率(%)',
            '预期收益率_百分比': '预期收益率(%)',
            '置信等级': '置信等级',
            '预测规则': '使用的预测规则',
            '预测依据': '预测依据',
            '收益偏差_百分比': '收益偏差(%)'
        }

        output_df = df[available_columns].copy()
        output_df = output_df.rename(columns=column_rename)

        # 按信号等级和概率排序
        signal_priority = {
            '🎯 完美信号': 1, '💎 钻石级组合': 2, '🥇 黄金信号': 3,
            '🔥 热点信号': 4, '🥈 稳健信号': 5, '📊 放量信号': 6,
            '🥉 基础信号': 7, '⚠️ 风险信号': 8, '➡️ 普通信号': 9
        }
        output_df['信号优先级'] = output_df['信号等级'].map(signal_priority)

        # 如果有校验数据，优先显示校验成功的股票
        if has_validation and '校验状态' in output_df.columns:
            output_df['校验优先级'] = output_df['校验状态'].apply(
                lambda x: 1 if '✅' in x and '盈利' in x else
                         2 if '✅' in x else
                         3 if '❌' in x and '盈利' in x else
                         4 if '❌' in x else 5
            )
            output_df = output_df.sort_values(['校验优先级', '信号优先级', '明天上涨概率(%)'], ascending=[True, True, False])
            output_df = output_df.drop(['校验优先级', '信号优先级'], axis=1)
        else:
            output_df = output_df.sort_values(['信号优先级', '明天上涨概率(%)'], ascending=[True, False])
            output_df = output_df.drop('信号优先级', axis=1)

        if output_path.endswith('.xlsx'):
            output_df.to_excel(output_path, index=False)
        else:
            output_df.to_csv(output_path, index=False, encoding='utf-8-sig')

        validation_text = "带校验" if has_validation else "无校验"
        print(f"💾 {validation_text}预测结果已保存到: {output_path}")

def main():
    """主函数"""
    predictor = PredictionWithValidation()

    # 预测用户数据目录中的文件
    prediction_dir = "预测文件资料"

    if not os.path.exists(prediction_dir):
        print(f"❌ 预测数据目录不存在: {prediction_dir}")
        return

    # 获取目录中的所有Excel文件 (排除已生成的预测文件)
    files = [f for f in os.listdir(prediction_dir)
             if f.endswith(('.xlsx', '.xls', '.csv'))
             and not f.endswith('_上涨概率预测.xlsx')
             and not f.endswith('_高级多特征预测.xlsx')
             and not f.endswith('_信号等级预测.xlsx')
             and not f.endswith('_全面信号预测.xlsx')
             and not f.endswith('_带校验预测.xlsx')
             and not f.startswith('~')]

    if not files:
        print(f"❌ 在 {prediction_dir} 目录中未找到数据文件")
        return

    print(f"📂 发现 {len(files)} 个数据文件:")
    for i, file in enumerate(files, 1):
        print(f"  {i}. {file}")

    # 处理每个文件
    for file_name in files:
        file_path = os.path.join(prediction_dir, file_name)

        # 从文件名提取日期
        date_str = None
        if '2025-06-30' in file_name:
            date_str = '2025-06-30'
        elif '2025-07-01' in file_name:
            date_str = '2025-07-01'
        elif '2025-07-02' in file_name:
            date_str = '2025-07-02'
        elif '2025-07-03' in file_name:
            date_str = '2025-07-03'
        elif '2025-07-04' in file_name:
            date_str = '2025-07-04'

        if date_str is None:
            print(f"⚠️ 无法从文件名 {file_name} 提取日期，跳过")
            continue

        print(f"\n" + "="*80)
        print(f"📊 预测并校验: {file_name} ({date_str})")
        print(f"="*80)

        try:
            # 预测并校验
            df, has_validation = predictor.predict_and_validate(file_path, date_str)

            if df is not None:
                # 分析结果
                df = predictor.analyze_validation_results(df, has_validation)

                # 保存结果
                if file_path.endswith('.xlsx'):
                    output_path = file_path.replace('.xlsx', '_带校验预测.xlsx')
                elif file_path.endswith('.xls'):
                    output_path = file_path.replace('.xls', '_带校验预测.xlsx')
                elif file_path.endswith('.csv'):
                    output_path = file_path.replace('.csv', '_带校验预测.csv')
                else:
                    output_path = file_path + '_带校验预测.xlsx'

                predictor.save_prediction_with_validation(df, output_path, has_validation)

                validation_text = "带校验" if has_validation else "无校验"
                print(f"✅ {file_name} {validation_text}预测完成！")

        except Exception as e:
            print(f"❌ 预测 {file_name} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue

    print(f"\n🎉 所有文件的预测和校验完成！")
    print(f"🎯 6月30日的预测已进行实际校验")
    print(f"💰 您可以查看校验结果了解实际买卖情况")

if __name__ == "__main__":
    main()
