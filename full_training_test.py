#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整训练测试 - 评估真实准确率
包含方向准确率、回归准确率等多项指标

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from integrated_data_processor import IntegratedStockDataProcessor
from multimodal_model import MultiModalStockPredictor
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    # 将连续值转换为涨跌方向
    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)
    
    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)
    
    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def calculate_regression_metrics(y_true, y_pred):
    """计算回归指标"""
    mse = mean_squared_error(y_true, y_pred)
    mae = mean_absolute_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_true, y_pred)
    
    # 计算MAPE (避免除零)
    mask = y_true != 0
    mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100 if np.any(mask) else 0
    
    return {
        'mse': mse,
        'mae': mae,
        'rmse': rmse,
        'r2': r2,
        'mape': mape
    }

def full_training_evaluation():
    """完整训练和评估"""
    print("=" * 80)
    print("🎯 完整训练和准确率评估")
    print("=" * 80)
    
    try:
        # 1. 数据准备
        print("1. 准备数据...")
        data_processor = IntegratedStockDataProcessor(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=20  # 使用标准序列长度
        )
        
        # 加载和处理数据
        stock_df = data_processor.load_stock_data()
        tech_df = data_processor.load_tech_strength_data()
        merged_df = data_processor.merge_datasets(stock_df, tech_df)
        
        # 创建基本特征
        merged_df = data_processor.create_price_features(merged_df)
        merged_df = data_processor.process_tech_strength_features(merged_df)
        
        # 使用基本特征避免过多NaN
        basic_price_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '成交量', '成交额', '换手率',
            '价格变化率', '高低价差比', '开收价差比', 'SMA_5', 'SMA_20', 'RSI', 'MACD'
        ]
        
        basic_tech_features = [
            '技术强度', '技术强度_等级', '技术强度_normalized',
            '技术强度_极弱', '技术强度_弱', '技术强度_中等偏弱',
            '技术强度_中等偏强', '技术强度_强', '技术强度_极强',
            '连续强度_3天_ratio', '连续强度_5天_ratio', '连续强度_10天_ratio'
        ]
        
        # 更新特征列
        data_processor.price_feature_columns = [col for col in basic_price_features if col in merged_df.columns]
        data_processor.tech_feature_columns = [col for col in basic_tech_features if col in merged_df.columns]
        
        # 准备数据
        required_columns = (data_processor.price_feature_columns + 
                          data_processor.tech_feature_columns + 
                          [data_processor.target_column, '股票代码', '日期'])
        
        available_columns = [col for col in required_columns if col in merged_df.columns]
        merged_df = merged_df[available_columns].dropna()
        
        print(f"   最终数据量: {len(merged_df)} 条记录")
        print(f"   价格特征数: {len(data_processor.price_feature_columns)}")
        print(f"   技术强度特征数: {len(data_processor.tech_feature_columns)}")
        
        # 创建序列
        X_price, X_tech, y = data_processor.create_sequences(merged_df)
        print(f"   序列数量: {len(X_price)}")
        
        # 2. 数据分割
        print("\n2. 分割训练/验证/测试集...")
        
        # 按时间顺序分割 (70% 训练, 15% 验证, 15% 测试)
        n_samples = len(X_price)
        train_size = int(n_samples * 0.7)
        val_size = int(n_samples * 0.15)
        
        X_price_train = X_price[:train_size]
        X_tech_train = X_tech[:train_size]
        y_train = y[:train_size]
        
        X_price_val = X_price[train_size:train_size+val_size]
        X_tech_val = X_tech[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        
        X_price_test = X_price[train_size+val_size:]
        X_tech_test = X_tech[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"   训练集: {len(X_price_train)} 样本")
        print(f"   验证集: {len(X_price_val)} 样本")
        print(f"   测试集: {len(X_price_test)} 样本")
        
        # 3. 数据标准化
        print("\n3. 数据标准化...")
        X_price_train_scaled, X_tech_train_scaled, y_train_scaled = data_processor.normalize_data(
            X_price_train, X_tech_train, y_train, fit_scalers=True
        )
        X_price_val_scaled, X_tech_val_scaled, y_val_scaled = data_processor.normalize_data(
            X_price_val, X_tech_val, y_val, fit_scalers=False
        )
        X_price_test_scaled, X_tech_test_scaled, y_test_scaled = data_processor.normalize_data(
            X_price_test, X_tech_test, y_test, fit_scalers=False
        )
        
        # 4. 创建和训练模型
        print("\n4. 创建和训练模型...")
        model = MultiModalStockPredictor(
            sequence_length=data_processor.sequence_length,
            price_features=X_price_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[64, 128],
            lstm_units=[128, 64],
            attention_heads=8,
            dropout_rate=0.3
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.001)
        
        print("   开始训练...")
        history = model.train(
            X_price_train_scaled, X_tech_train_scaled, y_train_scaled,
            X_price_val_scaled, X_tech_val_scaled, y_val_scaled,
            epochs=10,  # 训练10个epoch
            batch_size=128
        )
        
        # 5. 模型评估
        print("\n5. 模型评估...")
        
        # 在测试集上预测
        y_pred_scaled = model.predict(X_price_test_scaled, X_tech_test_scaled)
        
        # 反标准化预测结果
        y_pred = data_processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = data_processor.inverse_transform_target(y_test_scaled)
        
        # 计算各种准确率指标
        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        regression_metrics = calculate_regression_metrics(y_test_original, y_pred)
        
        # 6. 结果展示
        print("\n" + "=" * 80)
        print("📊 最终评估结果")
        print("=" * 80)
        
        print(f"\n🎯 方向预测准确率:")
        print(f"   方向准确率: {direction_metrics['direction_accuracy']:.4f} ({direction_metrics['direction_accuracy']*100:.2f}%)")
        print(f"   精确率: {direction_metrics['precision']:.4f}")
        print(f"   召回率: {direction_metrics['recall']:.4f}")
        print(f"   F1分数: {direction_metrics['f1_score']:.4f}")
        
        print(f"\n📈 回归预测准确率:")
        print(f"   平均绝对误差 (MAE): {regression_metrics['mae']:.6f}")
        print(f"   均方根误差 (RMSE): {regression_metrics['rmse']:.6f}")
        print(f"   R²决定系数: {regression_metrics['r2']:.6f}")
        print(f"   平均绝对百分比误差 (MAPE): {regression_metrics['mape']:.2f}%")
        
        # 分析预测分布
        print(f"\n📊 预测分布分析:")
        print(f"   真实涨跌分布: 上涨 {(y_test_original > 0).mean():.2%}, 下跌 {(y_test_original <= 0).mean():.2%}")
        print(f"   预测涨跌分布: 上涨 {(y_pred > 0).mean():.2%}, 下跌 {(y_pred <= 0).mean():.2%}")
        
        # 保存结果
        results = {
            'direction_accuracy': direction_metrics['direction_accuracy'],
            'precision': direction_metrics['precision'],
            'recall': direction_metrics['recall'],
            'f1_score': direction_metrics['f1_score'],
            'mae': regression_metrics['mae'],
            'rmse': regression_metrics['rmse'],
            'r2': regression_metrics['r2'],
            'mape': regression_metrics['mape'],
            'training_samples': len(X_price_train),
            'test_samples': len(X_price_test),
            'price_features': X_price_train.shape[-1],
            'tech_features': X_tech_train.shape[-1]
        }
        
        return True, results
        
    except Exception as e:
        print(f"❌ 完整训练评估失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    print("🚀 开始完整训练和准确率评估")
    
    start_time = datetime.now()
    success, results = full_training_evaluation()
    end_time = datetime.now()
    
    if success:
        print(f"\n⏱️ 总耗时: {end_time - start_time}")
        print(f"\n🎉 评估完成! 主要指标:")
        print(f"   📍 方向准确率: {results['direction_accuracy']*100:.2f}%")
        print(f"   📍 平均绝对误差: {results['mae']:.6f}")
        print(f"   📍 R²决定系数: {results['r2']:.6f}")
    else:
        print("❌ 评估失败")

if __name__ == "__main__":
    main()
