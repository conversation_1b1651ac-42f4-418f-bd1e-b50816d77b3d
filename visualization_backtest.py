import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class VisualizationBacktest:
    """可视化和回测分析模块"""
    
    def __init__(self):
        """初始化可视化回测模块"""
        self.backtest_results = None
        self.portfolio_history = []
        
    def plot_prediction_accuracy(self, 
                                predictions: Dict[str, float], 
                                actual_returns: Dict[str, float],
                                save_path: str = None):
        """
        绘制预测准确性分析
        
        Args:
            predictions: 预测结果
            actual_returns: 实际收益率
            save_path: 保存路径
        """
        # 匹配预测和实际结果
        matched_data = []
        for stock_code in predictions.keys():
            if stock_code in actual_returns:
                matched_data.append({
                    'stock_code': stock_code,
                    'predicted': predictions[stock_code],
                    'actual': actual_returns[stock_code]
                })
        
        if not matched_data:
            print("没有匹配的预测和实际数据")
            return
        
        df = pd.DataFrame(matched_data)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('预测准确性分析', fontsize=16, fontweight='bold')
        
        # 预测vs实际散点图
        axes[0, 0].scatter(df['actual'], df['predicted'], alpha=0.6, s=50)
        axes[0, 0].plot([df['actual'].min(), df['actual'].max()], 
                       [df['actual'].min(), df['actual'].max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('实际收益率')
        axes[0, 0].set_ylabel('预测收益率')
        axes[0, 0].set_title('预测值 vs 实际值')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 计算相关系数
        correlation = df['predicted'].corr(df['actual'])
        axes[0, 0].text(0.05, 0.95, f'相关系数: {correlation:.3f}', 
                       transform=axes[0, 0].transAxes, fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        
        # 方向准确率分析
        direction_correct = np.sign(df['predicted']) == np.sign(df['actual'])
        direction_accuracy = direction_correct.mean()
        
        direction_counts = pd.Series(direction_correct).value_counts()
        axes[0, 1].pie(direction_counts.values, labels=['错误', '正确'], autopct='%1.1f%%',
                      colors=['lightcoral', 'lightgreen'])
        axes[0, 1].set_title(f'方向预测准确率: {direction_accuracy:.1%}')
        
        # 预测误差分布
        errors = df['predicted'] - df['actual']
        axes[1, 0].hist(errors, bins=30, alpha=0.7, edgecolor='black', color='skyblue')
        axes[1, 0].axvline(errors.mean(), color='red', linestyle='--', 
                          label=f'平均误差: {errors.mean():.4f}')
        axes[1, 0].set_xlabel('预测误差')
        axes[1, 0].set_ylabel('频数')
        axes[1, 0].set_title('预测误差分布')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 分区间准确率
        df['predicted_range'] = pd.cut(df['predicted'], bins=5, labels=['很低', '低', '中', '高', '很高'])
        range_accuracy = df.groupby('predicted_range').apply(
            lambda x: (np.sign(x['predicted']) == np.sign(x['actual'])).mean()
        )
        
        axes[1, 1].bar(range_accuracy.index, range_accuracy.values, 
                      color='lightblue', edgecolor='black')
        axes[1, 1].set_xlabel('预测收益率区间')
        axes[1, 1].set_ylabel('方向准确率')
        axes[1, 1].set_title('不同预测区间的准确率')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"预测准确性分析图已保存到: {save_path}")
        
        plt.show()
        
        # 打印统计信息
        print("\n预测准确性统计:")
        print(f"样本数量: {len(df)}")
        print(f"相关系数: {correlation:.4f}")
        print(f"方向准确率: {direction_accuracy:.4f}")
        print(f"平均绝对误差: {abs(errors).mean():.4f}")
        print(f"均方根误差: {np.sqrt((errors**2).mean()):.4f}")
    
    def plot_portfolio_performance(self, 
                                 portfolio_history: List[Dict],
                                 benchmark_returns: List[float] = None,
                                 save_path: str = None):
        """
        绘制投资组合表现
        
        Args:
            portfolio_history: 投资组合历史数据
            benchmark_returns: 基准收益率
            save_path: 保存路径
        """
        if not portfolio_history:
            print("没有投资组合历史数据")
            return
        
        df = pd.DataFrame(portfolio_history)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('投资组合表现分析', fontsize=16, fontweight='bold')
        
        # 累计收益曲线
        if 'cumulative_return' in df.columns:
            axes[0, 0].plot(df.index, df['cumulative_return'], label='策略收益', linewidth=2)
            
            if benchmark_returns:
                benchmark_cumulative = np.cumprod(1 + np.array(benchmark_returns)) - 1
                axes[0, 0].plot(df.index, benchmark_cumulative[:len(df)], 
                               label='基准收益', linewidth=2, alpha=0.7)
            
            axes[0, 0].set_xlabel('时间')
            axes[0, 0].set_ylabel('累计收益率')
            axes[0, 0].set_title('累计收益曲线')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
        
        # 回撤分析
        if 'drawdown' in df.columns:
            axes[0, 1].fill_between(df.index, df['drawdown'], 0, 
                                   color='red', alpha=0.3, label='回撤')
            axes[0, 1].set_xlabel('时间')
            axes[0, 1].set_ylabel('回撤')
            axes[0, 1].set_title('回撤分析')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # 收益分布
        if 'daily_return' in df.columns:
            axes[1, 0].hist(df['daily_return'], bins=30, alpha=0.7, 
                           edgecolor='black', color='lightgreen')
            axes[1, 0].axvline(df['daily_return'].mean(), color='red', linestyle='--',
                              label=f'平均收益: {df["daily_return"].mean():.4f}')
            axes[1, 0].set_xlabel('日收益率')
            axes[1, 0].set_ylabel('频数')
            axes[1, 0].set_title('收益分布')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        # 滚动夏普比率
        if 'rolling_sharpe' in df.columns:
            axes[1, 1].plot(df.index, df['rolling_sharpe'], color='purple', linewidth=2)
            axes[1, 1].axhline(y=1, color='red', linestyle='--', alpha=0.7, label='夏普比率=1')
            axes[1, 1].set_xlabel('时间')
            axes[1, 1].set_ylabel('滚动夏普比率')
            axes[1, 1].set_title('滚动夏普比率')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"投资组合表现图已保存到: {save_path}")
        
        plt.show()
    
    def plot_trading_analysis(self, trade_history: List[Dict], save_path: str = None):
        """
        绘制交易分析
        
        Args:
            trade_history: 交易历史
            save_path: 保存路径
        """
        if not trade_history:
            print("没有交易历史数据")
            return
        
        df = pd.DataFrame(trade_history)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('交易分析', fontsize=16, fontweight='bold')
        
        # 盈亏分布
        axes[0, 0].hist(df['actual_return'], bins=30, alpha=0.7, 
                       edgecolor='black', color='lightblue')
        axes[0, 0].axvline(0, color='red', linestyle='--', alpha=0.7)
        axes[0, 0].set_xlabel('实际收益率')
        axes[0, 0].set_ylabel('频数')
        axes[0, 0].set_title('交易收益分布')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 预测vs实际收益
        axes[0, 1].scatter(df['predicted_return'], df['actual_return'], alpha=0.6)
        axes[0, 1].plot([df['predicted_return'].min(), df['predicted_return'].max()],
                       [df['predicted_return'].min(), df['predicted_return'].max()], 'r--')
        axes[0, 1].set_xlabel('预测收益率')
        axes[0, 1].set_ylabel('实际收益率')
        axes[0, 1].set_title('预测vs实际收益')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 累计盈亏
        df['cumulative_pnl'] = df['profit_loss'].cumsum()
        axes[0, 2].plot(df.index, df['cumulative_pnl'], linewidth=2, color='green')
        axes[0, 2].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[0, 2].set_xlabel('交易次数')
        axes[0, 2].set_ylabel('累计盈亏')
        axes[0, 2].set_title('累计盈亏曲线')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 胜率统计
        win_trades = len(df[df['actual_return'] > 0])
        total_trades = len(df)
        win_rate = win_trades / total_trades
        
        labels = ['盈利交易', '亏损交易']
        sizes = [win_trades, total_trades - win_trades]
        colors = ['lightgreen', 'lightcoral']
        
        axes[1, 0].pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors)
        axes[1, 0].set_title(f'胜率: {win_rate:.1%}')
        
        # 持仓时间分析（如果有时间戳）
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['month'] = df['timestamp'].dt.month
            monthly_returns = df.groupby('month')['actual_return'].mean()
            
            axes[1, 1].bar(monthly_returns.index, monthly_returns.values, 
                          color='skyblue', edgecolor='black')
            axes[1, 1].set_xlabel('月份')
            axes[1, 1].set_ylabel('平均收益率')
            axes[1, 1].set_title('月度平均收益')
            axes[1, 1].grid(True, alpha=0.3)
        
        # 收益风险散点图
        if len(df) > 10:  # 确保有足够的数据点
            # 按股票分组计算收益和风险
            stock_stats = df.groupby('stock_code').agg({
                'actual_return': ['mean', 'std', 'count']
            }).round(4)
            
            stock_stats.columns = ['avg_return', 'volatility', 'trade_count']
            stock_stats = stock_stats[stock_stats['trade_count'] >= 2]  # 至少2次交易
            
            if len(stock_stats) > 0:
                scatter = axes[1, 2].scatter(stock_stats['volatility'], stock_stats['avg_return'],
                                           s=stock_stats['trade_count']*20, alpha=0.6)
                axes[1, 2].axhline(y=0, color='red', linestyle='--', alpha=0.7)
                axes[1, 2].axvline(x=stock_stats['volatility'].median(), color='blue', 
                                  linestyle='--', alpha=0.7)
                axes[1, 2].set_xlabel('收益波动率')
                axes[1, 2].set_ylabel('平均收益率')
                axes[1, 2].set_title('收益-风险分析')
                axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"交易分析图已保存到: {save_path}")
        
        plt.show()
        
        # 打印交易统计
        print("\n交易统计:")
        print(f"总交易次数: {len(df)}")
        print(f"盈利交易: {win_trades} ({win_rate:.1%})")
        print(f"平均收益率: {df['actual_return'].mean():.4f}")
        print(f"最大单次收益: {df['actual_return'].max():.4f}")
        print(f"最大单次亏损: {df['actual_return'].min():.4f}")
        print(f"总盈亏: {df['profit_loss'].sum():.2f}")
        print(f"夏普比率: {df['actual_return'].mean() / df['actual_return'].std():.4f}")
    
    def create_interactive_dashboard(self, 
                                   predictions: Dict[str, float],
                                   signals: Dict[str, str],
                                   top_stocks: List[Tuple[str, float]]):
        """
        创建交互式仪表板
        
        Args:
            predictions: 预测结果
            signals: 交易信号
            top_stocks: 推荐股票
        """
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('预测收益率分布', '交易信号分布', '推荐股票TOP10', '预测强度分析'),
            specs=[[{"type": "histogram"}, {"type": "pie"}],
                   [{"type": "bar"}, {"type": "scatter"}]]
        )
        
        # 预测收益率分布
        pred_values = list(predictions.values())
        fig.add_trace(
            go.Histogram(x=pred_values, name="预测收益率", nbinsx=30),
            row=1, col=1
        )
        
        # 交易信号分布
        signal_counts = pd.Series(list(signals.values())).value_counts()
        fig.add_trace(
            go.Pie(labels=signal_counts.index, values=signal_counts.values, name="交易信号"),
            row=1, col=2
        )
        
        # 推荐股票TOP10
        if top_stocks:
            top_10 = top_stocks[:10]
            stock_codes = [stock[0] for stock in top_10]
            returns = [stock[1] for stock in top_10]
            
            fig.add_trace(
                go.Bar(x=stock_codes, y=returns, name="预测收益率"),
                row=2, col=1
            )
        
        # 预测强度分析
        pred_abs = [abs(x) for x in pred_values]
        fig.add_trace(
            go.Scatter(x=pred_values, y=pred_abs, mode='markers', 
                      name="预测强度", marker=dict(size=8, opacity=0.6)),
            row=2, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title_text="股票预测交互式仪表板",
            title_x=0.5,
            height=800,
            showlegend=False
        )
        
        # 显示图表
        fig.show()
        
        return fig
    
    def generate_performance_report(self, 
                                  trade_history: List[Dict],
                                  portfolio_history: List[Dict] = None) -> Dict:
        """
        生成性能报告
        
        Args:
            trade_history: 交易历史
            portfolio_history: 投资组合历史
            
        Returns:
            性能报告字典
        """
        if not trade_history:
            return {"error": "没有交易历史数据"}
        
        df = pd.DataFrame(trade_history)
        
        # 基础统计
        total_trades = len(df)
        winning_trades = len(df[df['actual_return'] > 0])
        win_rate = winning_trades / total_trades
        
        # 收益统计
        total_return = df['actual_return'].sum()
        avg_return = df['actual_return'].mean()
        max_return = df['actual_return'].max()
        min_return = df['actual_return'].min()
        
        # 风险统计
        volatility = df['actual_return'].std()
        sharpe_ratio = avg_return / volatility if volatility > 0 else 0
        
        # 最大回撤
        cumulative_returns = (1 + df['actual_return']).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 预测准确性
        direction_accuracy = np.mean(np.sign(df['predicted_return']) == np.sign(df['actual_return']))
        
        report = {
            "交易统计": {
                "总交易次数": total_trades,
                "盈利交易次数": winning_trades,
                "亏损交易次数": total_trades - winning_trades,
                "胜率": f"{win_rate:.2%}"
            },
            "收益统计": {
                "总收益率": f"{total_return:.4f}",
                "平均收益率": f"{avg_return:.4f}",
                "最大单次收益": f"{max_return:.4f}",
                "最大单次亏损": f"{min_return:.4f}"
            },
            "风险统计": {
                "收益波动率": f"{volatility:.4f}",
                "夏普比率": f"{sharpe_ratio:.4f}",
                "最大回撤": f"{max_drawdown:.4f}"
            },
            "预测准确性": {
                "方向准确率": f"{direction_accuracy:.2%}",
                "预测收益相关性": f"{df['predicted_return'].corr(df['actual_return']):.4f}"
            }
        }
        
        return report
