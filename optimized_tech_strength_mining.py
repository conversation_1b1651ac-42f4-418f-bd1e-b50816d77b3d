#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的技术强度数据挖掘 - 使用分段处理的连续性指标
主要数据源: 技术强度数据 (从交易明细抽取的指标)
辅助数据源: 股票交易明细 (基础交易数据)

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_model import MultiModalStockPredictor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class OptimizedTechStrengthProcessor:
    """优化的技术强度处理器 - 使用分段处理"""
    
    def __init__(self, tech_strength_path, stock_data_path, sequence_length=20):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.sequence_length = sequence_length
        
        # 标准化器
        self.tech_scaler = StandardScaler()
        self.auxiliary_scaler = StandardScaler()
        self.target_scaler = StandardScaler()
        
        # 标签编码器
        self.label_encoders = {}
    
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("=" * 60)
        print("🔍 加载和准备优化数据")
        print("=" * 60)
        
        # 1. 加载技术强度数据 (主要数据源)
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        # 2. 加载股票交易明细 (辅助数据源)
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"技术强度数据: {len(tech_df)} 条记录")
        print(f"股票交易明细: {len(stock_df)} 条记录")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 3. 合并数据 (以技术强度为主)
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='left',
            suffixes=('_tech', '_stock')
        )
        
        print(f"合并后数据: {len(merged_df)} 条记录")
        
        # 4. 准备技术强度特征 (主要特征)
        tech_strength_features = ['技术强度']  # 核心指标保持原值
        
        # 5. 处理连续性指标 - 三等分分段
        print("\n🔧 处理连续性指标分段:")
        
        # 只使用一个连续技术强度指标 (因为3天、5天、10天数据相同)
        continuous_features = [
            '连续技术强度3天数',  # 使用3天数据代表连续性
            '成交量是前一日几倍'   # 成交量倍数
        ]
        
        for feature in continuous_features:
            if feature in merged_df.columns:
                min_val = merged_df[feature].min()
                max_val = merged_df[feature].max()
                
                print(f"  {feature}: 范围 [{min_val:.2f}, {max_val:.2f}]")
                
                # 三等分分段
                cut_points = [min_val - 0.1, 
                             min_val + (max_val - min_val) / 3,
                             min_val + 2 * (max_val - min_val) / 3,
                             max_val + 0.1]
                
                merged_df[f'{feature}_分段'] = pd.cut(
                    merged_df[feature], 
                    bins=cut_points,
                    labels=[1, 2, 3],  # 1:低, 2:中, 3:高
                    include_lowest=True
                ).astype(float)
                
                tech_strength_features.append(f'{feature}_分段')
                
                # 统计分段分布
                segment_counts = merged_df[f'{feature}_分段'].value_counts().sort_index()
                total = len(merged_df)
                print(f"    分段: 低({segment_counts.get(1.0, 0)}, {segment_counts.get(1.0, 0)/total*100:.1f}%) "
                      f"中({segment_counts.get(2.0, 0)}, {segment_counts.get(2.0, 0)/total*100:.1f}%) "
                      f"高({segment_counts.get(3.0, 0)}, {segment_counts.get(3.0, 0)/total*100:.1f}%)")
        
        # 6. 处理分类特征
        print("\n🏷️ 处理分类特征:")
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        
        for col in categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                tech_strength_features.append(f'{col}_encoded')
                
                # 显示分类分布
                unique_count = merged_df[f'{col}_encoded'].nunique()
                print(f"  {col}: {unique_count} 个类别")
        
        # 7. 准备辅助特征 (基础交易数据)
        auxiliary_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价',
            '成交额', '换手率'
        ]
        
        # 辅助分类特征
        aux_categorical_features = ['复权状态', '交易状态', '是否ST股']
        for col in aux_categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                auxiliary_features.append(f'{col}_encoded')
        
        # 8. 确保特征列存在并处理缺失值
        available_tech_features = [col for col in tech_strength_features if col in merged_df.columns]
        available_auxiliary_features = [col for col in auxiliary_features if col in merged_df.columns]
        
        for col in available_tech_features + available_auxiliary_features:
            if merged_df[col].dtype in ['float64', 'int64']:
                merged_df[col] = merged_df[col].fillna(merged_df[col].median())
        
        # 9. 准备最终数据
        target_column = '涨跌幅_tech'
        feature_columns = available_tech_features + available_auxiliary_features + ['股票代码', '日期', target_column]
        final_df = merged_df[feature_columns].copy().dropna(subset=[target_column])
        
        print(f"\n📊 最终特征统计:")
        print(f"  技术强度特征: {len(available_tech_features)} 个")
        print(f"  辅助特征: {len(available_auxiliary_features)} 个")
        print(f"  总特征数: {len(available_tech_features) + len(available_auxiliary_features)}")
        print(f"  最终数据: {len(final_df)} 条记录")
        
        return final_df, available_tech_features, available_auxiliary_features, target_column
    
    def create_sequences(self, df, tech_features, auxiliary_features, target_column):
        """创建时间序列"""
        print(f"\n⏰ 创建时间序列 (序列长度: {self.sequence_length})...")
        
        X_tech_list = []
        X_auxiliary_list = []
        y_list = []
        
        for _, group in df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            if len(group) < self.sequence_length + 1:
                continue
            
            tech_data = group[tech_features].values
            auxiliary_data = group[auxiliary_features].values
            target_data = group[target_column].values
            
            for i in range(len(group) - self.sequence_length):
                X_tech_list.append(tech_data[i:i+self.sequence_length])
                X_auxiliary_list.append(auxiliary_data[i:i+self.sequence_length])
                y_list.append(target_data[i+self.sequence_length])
        
        X_tech = np.array(X_tech_list)
        X_auxiliary = np.array(X_auxiliary_list)
        y = np.array(y_list)
        
        print(f"  创建序列: {len(X_tech)} 个样本")
        print(f"  技术强度特征形状: {X_tech.shape}")
        print(f"  辅助特征形状: {X_auxiliary.shape}")
        
        return X_tech, X_auxiliary, y
    
    def normalize_data(self, X_tech, X_auxiliary, y, fit_scalers=False):
        """标准化数据"""
        if fit_scalers:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.tech_scaler.fit_transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.fit_transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.tech_scaler.transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.transform(y.reshape(-1, 1)).flatten()
        
        X_tech_scaled = X_tech_scaled.reshape(X_tech.shape)
        X_auxiliary_scaled = X_auxiliary_scaled.reshape(X_auxiliary.shape)
        
        return X_tech_scaled, X_auxiliary_scaled, y_scaled
    
    def inverse_transform_target(self, y_scaled):
        """反标准化目标变量"""
        return self.target_scaler.inverse_transform(y_scaled.reshape(-1, 1)).flatten()

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)
    
    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)
    
    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def optimized_tech_strength_test():
    """优化的技术强度数据挖掘测试"""
    print("🚀 开始优化的技术强度数据挖掘测试")
    
    try:
        # 1. 初始化处理器
        processor = OptimizedTechStrengthProcessor(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily",
            sequence_length=15  # 使用较短序列进行快速测试
        )
        
        # 2. 加载和准备数据
        final_df, tech_features, auxiliary_features, target_column = processor.load_and_prepare_data()
        
        # 3. 创建序列
        X_tech, X_auxiliary, y = processor.create_sequences(final_df, tech_features, auxiliary_features, target_column)
        
        # 4. 数据分割
        print(f"\n📊 数据分割...")
        n_samples = len(X_tech)
        train_size = int(n_samples * 0.8)
        
        X_tech_train, X_tech_test = X_tech[:train_size], X_tech[train_size:]
        X_auxiliary_train, X_auxiliary_test = X_auxiliary[:train_size], X_auxiliary[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        print(f"  训练集: {len(X_tech_train)} 样本")
        print(f"  测试集: {len(X_tech_test)} 样本")
        
        # 5. 数据标准化
        print(f"\n🔧 数据标准化...")
        X_tech_train_scaled, X_auxiliary_train_scaled, y_train_scaled = processor.normalize_data(
            X_tech_train, X_auxiliary_train, y_train, fit_scalers=True
        )
        X_tech_test_scaled, X_auxiliary_test_scaled, y_test_scaled = processor.normalize_data(
            X_tech_test, X_auxiliary_test, y_test, fit_scalers=False
        )
        
        # 6. 创建和训练模型
        print(f"\n🏗️ 创建和训练模型...")
        model = MultiModalStockPredictor(
            sequence_length=processor.sequence_length,
            price_features=X_auxiliary_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[64, 128],
            lstm_units=[128, 64],
            attention_heads=8,
            dropout_rate=0.3
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.001)
        
        print(f"  模型参数量: {model.model.count_params()}")
        
        # 快速训练
        model.train(
            X_auxiliary_train_scaled, X_tech_train_scaled, y_train_scaled,
            epochs=8,
            batch_size=128
        )
        
        # 7. 模型评估
        print(f"\n📈 模型评估...")
        
        y_pred_scaled = model.predict(X_auxiliary_test_scaled, X_tech_test_scaled)
        y_pred = processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = processor.inverse_transform_target(y_test_scaled)
        
        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        
        # 8. 结果展示
        print("\n" + "=" * 80)
        print("📊 优化技术强度挖掘结果")
        print("=" * 80)
        
        print(f"\n🎯 方向预测准确率:")
        print(f"   方向准确率: {direction_metrics['direction_accuracy']:.4f} ({direction_metrics['direction_accuracy']*100:.2f}%)")
        print(f"   精确率: {direction_metrics['precision']:.4f}")
        print(f"   召回率: {direction_metrics['recall']:.4f}")
        print(f"   F1分数: {direction_metrics['f1_score']:.4f}")
        
        print(f"\n📊 优化效果:")
        print(f"   使用分段处理的连续性指标")
        print(f"   减少了数据稀疏性")
        print(f"   提高了特征的区分度")
        
        return True, direction_metrics['direction_accuracy']
        
    except Exception as e:
        print(f"❌ 优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    start_time = datetime.now()
    success, accuracy = optimized_tech_strength_test()
    end_time = datetime.now()
    
    if success:
        print(f"\n⏱️ 总耗时: {end_time - start_time}")
        print(f"\n🎉 优化测试完成!")
        print(f"   📍 最终准确率: {accuracy*100:.2f}%")
        
        baseline_accuracy = 64.81
        improvement = (accuracy * 100) - baseline_accuracy
        print(f"\n📈 相比基础版本 ({baseline_accuracy:.2f}%):")
        if improvement > 0:
            print(f"   ✅ 提升了 {improvement:.2f} 个百分点!")
        else:
            print(f"   ⚠️ 下降了 {abs(improvement):.2f} 个百分点")
    else:
        print("❌ 优化测试失败")

if __name__ == "__main__":
    main()
