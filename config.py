#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票预测系统配置文件
"""

import os

# 数据路径配置
DATA_CONFIG = {
    'stock_data_path': 'stock_data/daily',
    'tech_strength_path': 'tech_strength/daily',
    'model_save_dir': 'models',
    'results_save_dir': 'results',
    'logs_save_dir': 'logs'
}

# 模型参数配置
MODEL_CONFIG = {
    'sequence_length': 30,
    'cnn_filters': [64, 128, 256],
    'lstm_units': [128, 64],
    'attention_heads': 8,
    'dropout_rate': 0.3,
    'learning_rate': 0.001
}

# 训练参数配置
TRAINING_CONFIG = {
    'epochs': 100,
    'batch_size': 32,
    'validation_split': 0.2,
    'early_stopping_patience': 15,
    'reduce_lr_patience': 8,
    'reduce_lr_factor': 0.5,
    'min_lr': 1e-7
}

# 交易策略配置
TRADING_CONFIG = {
    'prediction_threshold': 0.02,  # 预测阈值
    'stop_loss': 0.05,            # 止损比例
    'take_profit': 0.08,          # 止盈比例
    'max_positions': 10,          # 最大持仓数量
    'position_size_method': 'kelly',  # 仓位管理方法
    'risk_free_rate': 0.03        # 无风险利率
}

# 特征工程配置
FEATURE_CONFIG = {
    'ma_windows': [5, 10, 20, 30],        # 移动平均窗口
    'ema_windows': [12, 26],              # 指数移动平均窗口
    'rsi_window': 14,                     # RSI窗口
    'bb_window': 20,                      # 布林带窗口
    'bb_std': 2,                          # 布林带标准差倍数
    'volatility_windows': [5, 10, 20],    # 波动率窗口
    'volume_ma_windows': [5, 10]          # 成交量移动平均窗口
}

# 可视化配置
VISUALIZATION_CONFIG = {
    'figure_size': (15, 10),
    'dpi': 300,
    'style': 'seaborn',
    'color_palette': 'husl',
    'font_family': ['SimHei', 'Microsoft YaHei'],
    'save_format': 'png'
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_handler': True,
    'console_handler': True
}

# 创建必要的目录
def create_directories():
    """创建必要的目录"""
    directories = [
        DATA_CONFIG['model_save_dir'],
        DATA_CONFIG['results_save_dir'],
        DATA_CONFIG['logs_save_dir']
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")

# 验证配置
def validate_config():
    """验证配置参数"""
    errors = []
    
    # 验证数据路径
    if not os.path.exists(DATA_CONFIG['stock_data_path']):
        errors.append(f"股票数据路径不存在: {DATA_CONFIG['stock_data_path']}")
    
    if not os.path.exists(DATA_CONFIG['tech_strength_path']):
        errors.append(f"技术强度数据路径不存在: {DATA_CONFIG['tech_strength_path']}")
    
    # 验证模型参数
    if MODEL_CONFIG['sequence_length'] <= 0:
        errors.append("序列长度必须大于0")
    
    if MODEL_CONFIG['dropout_rate'] < 0 or MODEL_CONFIG['dropout_rate'] >= 1:
        errors.append("Dropout率必须在[0, 1)范围内")
    
    if MODEL_CONFIG['learning_rate'] <= 0:
        errors.append("学习率必须大于0")
    
    # 验证训练参数
    if TRAINING_CONFIG['epochs'] <= 0:
        errors.append("训练轮数必须大于0")
    
    if TRAINING_CONFIG['batch_size'] <= 0:
        errors.append("批次大小必须大于0")
    
    if TRAINING_CONFIG['validation_split'] <= 0 or TRAINING_CONFIG['validation_split'] >= 1:
        errors.append("验证集比例必须在(0, 1)范围内")
    
    # 验证交易参数
    if TRADING_CONFIG['prediction_threshold'] <= 0:
        errors.append("预测阈值必须大于0")
    
    if TRADING_CONFIG['stop_loss'] <= 0 or TRADING_CONFIG['stop_loss'] >= 1:
        errors.append("止损比例必须在(0, 1)范围内")
    
    if TRADING_CONFIG['take_profit'] <= 0:
        errors.append("止盈比例必须大于0")
    
    if TRADING_CONFIG['max_positions'] <= 0:
        errors.append("最大持仓数量必须大于0")
    
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("✅ 配置验证通过")
    return True

# 打印配置信息
def print_config():
    """打印配置信息"""
    print("📋 当前配置:")
    print("\n数据配置:")
    for key, value in DATA_CONFIG.items():
        print(f"  {key}: {value}")
    
    print("\n模型配置:")
    for key, value in MODEL_CONFIG.items():
        print(f"  {key}: {value}")
    
    print("\n训练配置:")
    for key, value in TRAINING_CONFIG.items():
        print(f"  {key}: {value}")
    
    print("\n交易配置:")
    for key, value in TRADING_CONFIG.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    create_directories()
    print_config()
    validate_config()
