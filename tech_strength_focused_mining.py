#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
以技术强度为主的数据挖掘
主要数据源: 技术强度数据 (从交易明细抽取的指标)
辅助数据源: 股票交易明细 (基础交易数据)

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_model import MultiModalStockPredictor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class TechStrengthFocusedProcessor:
    """以技术强度为主的数据处理器"""
    
    def __init__(self, tech_strength_path, stock_data_path, sequence_length=20):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.sequence_length = sequence_length
        
        # 标准化器
        self.tech_scaler = StandardScaler()
        self.auxiliary_scaler = StandardScaler()
        self.target_scaler = StandardScaler()
        
        # 标签编码器
        self.label_encoders = {}
    
    def load_data(self):
        """加载数据 - 以技术强度为主"""
        print("加载数据 - 以技术强度数据为主...")
        
        # 1. 加载技术强度数据 (主要数据源)
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        # 2. 加载股票交易明细 (辅助数据源)
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"技术强度数据 (主要): {len(tech_df)} 条记录")
        print(f"股票交易明细 (辅助): {len(stock_df)} 条记录")

        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])

        # 显示技术强度数据的列
        print("\n技术强度数据的指标列:")
        for i, col in enumerate(tech_df.columns, 1):
            print(f"  {i:2d}. {col}")

        return tech_df, stock_df
    
    def prepare_features(self, tech_df, stock_df):
        """准备特征 - 以技术强度指标为主"""
        print("\n准备特征 - 以技术强度指标为主...")
        
        # 以技术强度数据为基础，左连接股票数据作为辅助
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='left',  # 以技术强度数据为主
            suffixes=('_tech', '_stock')
        )
        
        print(f"合并后数据: {len(merged_df)} 条记录")
        
        # === 主要特征: 技术强度相关指标 ===
        tech_strength_features = [
            '技术强度',                    # 核心技术强度指标 (保持原值，因为是分类)
        ]

        # 处理连续性指标 - 三等分分段
        continuous_features = [
            '连续技术强度3天数',
            '连续技术强度5天数',
            '连续技术强度10天数',
            '成交量是前一日几倍'  # 成交量倍数也进行分段
        ]

        for feature in continuous_features:
            if feature in merged_df.columns:
                # 计算最大值和最小值
                min_val = merged_df[feature].min()
                max_val = merged_df[feature].max()

                print(f"  {feature}: 范围 [{min_val}, {max_val}]")

                # 三等分分段
                # 第一段: 低值 (min ~ 1/3)
                # 第二段: 中值 (1/3 ~ 2/3)
                # 第三段: 高值 (2/3 ~ max)
                cut_points = [min_val - 0.1,
                             min_val + (max_val - min_val) / 3,
                             min_val + 2 * (max_val - min_val) / 3,
                             max_val + 0.1]

                merged_df[f'{feature}_分段'] = pd.cut(
                    merged_df[feature],
                    bins=cut_points,
                    labels=[1, 2, 3],  # 1:低, 2:中, 3:高
                    include_lowest=True
                ).astype(float)

                # 添加到特征列表
                tech_strength_features.append(f'{feature}_分段')

                # 统计分段分布
                segment_counts = merged_df[f'{feature}_分段'].value_counts().sort_index()
                print(f"    分段分布: 低值({segment_counts.get(1.0, 0)}) 中值({segment_counts.get(2.0, 0)}) 高值({segment_counts.get(3.0, 0)})")

        print(f"\n连续性指标三等分处理完成")
        
        # 技术强度分类特征 (需要编码)
        tech_categorical_features = [
            '技术指标特征',               # 技术指标组合特征
            '趋势组合',                   # 趋势组合特征
            '日内股票标记',               # 日内标记
        ]
        
        # === 辅助特征: 基础交易数据 ===
        auxiliary_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价',
            '成交量_stock', '成交额', '换手率'
        ]
        
        # 辅助分类特征
        auxiliary_categorical_features = [
            '复权状态', '交易状态', '是否ST股'
        ]
        
        # 处理技术强度分类特征
        for col in tech_categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                tech_strength_features.append(f'{col}_encoded')
        
        # 处理辅助分类特征
        for col in auxiliary_categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                auxiliary_features.append(f'{col}_encoded')
        
        # 确保特征列存在
        available_tech_features = [col for col in tech_strength_features if col in merged_df.columns]
        available_auxiliary_features = [col for col in auxiliary_features if col in merged_df.columns]
        
        print(f"\n可用技术强度特征: {len(available_tech_features)} 个")
        print("技术强度特征列表:")
        for i, feature in enumerate(available_tech_features, 1):
            print(f"  {i:2d}. {feature}")
        
        print(f"\n可用辅助特征: {len(available_auxiliary_features)} 个")
        print("辅助特征列表:")
        for i, feature in enumerate(available_auxiliary_features, 1):
            print(f"  {i:2d}. {feature}")
        
        # 处理缺失值
        for col in available_tech_features + available_auxiliary_features:
            if merged_df[col].dtype in ['float64', 'int64']:
                merged_df[col] = merged_df[col].fillna(merged_df[col].median())
        
        # 设置目标变量 (使用技术强度数据的涨跌幅)
        target_column = '涨跌幅_tech'
        
        # 准备最终数据
        feature_columns = available_tech_features + available_auxiliary_features + ['股票代码', '日期', target_column]
        final_df = merged_df[feature_columns].copy()
        
        # 删除目标变量中的异常值
        final_df = final_df.dropna(subset=[target_column])
        
        print(f"\n最终数据: {len(final_df)} 条记录")
        print(f"目标变量: {target_column}")
        
        return final_df, available_tech_features, available_auxiliary_features, target_column
    
    def create_sequences(self, df, tech_features, auxiliary_features, target_column):
        """创建时间序列"""
        print("\n创建时间序列...")
        
        X_tech_list = []      # 主要特征: 技术强度
        X_auxiliary_list = [] # 辅助特征: 交易明细
        y_list = []
        
        # 按股票分组创建序列
        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            if len(group) < self.sequence_length + 1:
                continue
            
            # 提取特征数据
            tech_data = group[tech_features].values
            auxiliary_data = group[auxiliary_features].values
            target_data = group[target_column].values
            
            # 创建序列
            for i in range(len(group) - self.sequence_length):
                X_tech_list.append(tech_data[i:i+self.sequence_length])
                X_auxiliary_list.append(auxiliary_data[i:i+self.sequence_length])
                y_list.append(target_data[i+self.sequence_length])
        
        X_tech = np.array(X_tech_list)
        X_auxiliary = np.array(X_auxiliary_list)
        y = np.array(y_list)
        
        print(f"创建了 {len(X_tech)} 个序列样本")
        print(f"技术强度特征形状: {X_tech.shape}")
        print(f"辅助特征形状: {X_auxiliary.shape}")
        print(f"目标形状: {y.shape}")
        
        return X_tech, X_auxiliary, y
    
    def normalize_data(self, X_tech, X_auxiliary, y, fit_scalers=False):
        """标准化数据"""
        if fit_scalers:
            # 重塑数据进行标准化
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            # 拟合标准化器
            X_tech_scaled = self.tech_scaler.fit_transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.fit_transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            # 使用已拟合的标准化器
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.tech_scaler.transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.transform(y.reshape(-1, 1)).flatten()
        
        # 重塑回原始形状
        X_tech_scaled = X_tech_scaled.reshape(X_tech.shape)
        X_auxiliary_scaled = X_auxiliary_scaled.reshape(X_auxiliary.shape)
        
        return X_tech_scaled, X_auxiliary_scaled, y_scaled
    
    def inverse_transform_target(self, y_scaled):
        """反标准化目标变量"""
        return self.target_scaler.inverse_transform(y_scaled.reshape(-1, 1)).flatten()

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)
    
    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)
    
    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def tech_strength_focused_test():
    """以技术强度为主的数据挖掘测试"""
    print("=" * 80)
    print("🎯 以技术强度为主的数据挖掘测试")
    print("=" * 80)
    
    try:
        # 1. 初始化处理器
        processor = TechStrengthFocusedProcessor(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily",
            sequence_length=20
        )
        
        # 2. 加载数据
        tech_df, stock_df = processor.load_data()
        
        # 3. 准备特征
        final_df, tech_features, auxiliary_features, target_column = processor.prepare_features(tech_df, stock_df)
        
        # 4. 创建序列
        X_tech, X_auxiliary, y = processor.create_sequences(final_df, tech_features, auxiliary_features, target_column)
        
        # 5. 数据分割
        print("\n数据分割...")
        n_samples = len(X_tech)
        train_size = int(n_samples * 0.7)
        val_size = int(n_samples * 0.15)
        
        X_tech_train = X_tech[:train_size]
        X_auxiliary_train = X_auxiliary[:train_size]
        y_train = y[:train_size]
        
        X_tech_val = X_tech[train_size:train_size+val_size]
        X_auxiliary_val = X_auxiliary[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        
        X_tech_test = X_tech[train_size+val_size:]
        X_auxiliary_test = X_auxiliary[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"训练集: {len(X_tech_train)} 样本")
        print(f"验证集: {len(X_tech_val)} 样本")
        print(f"测试集: {len(X_tech_test)} 样本")
        
        # 6. 数据标准化
        print("\n数据标准化...")
        X_tech_train_scaled, X_auxiliary_train_scaled, y_train_scaled = processor.normalize_data(
            X_tech_train, X_auxiliary_train, y_train, fit_scalers=True
        )
        X_tech_val_scaled, X_auxiliary_val_scaled, y_val_scaled = processor.normalize_data(
            X_tech_val, X_auxiliary_val, y_val, fit_scalers=False
        )
        X_tech_test_scaled, X_auxiliary_test_scaled, y_test_scaled = processor.normalize_data(
            X_tech_test, X_auxiliary_test, y_test, fit_scalers=False
        )
        
        # 7. 创建和训练模型
        print("\n创建和训练模型...")
        model = MultiModalStockPredictor(
            sequence_length=processor.sequence_length,
            price_features=X_auxiliary_train.shape[-1],  # 辅助特征作为"价格"输入
            tech_features=X_tech_train.shape[-1],        # 技术强度作为"技术"输入
            cnn_filters=[64, 128, 256],
            lstm_units=[128, 64],
            attention_heads=8,
            dropout_rate=0.3
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.001)
        
        print(f"模型参数量: {model.model.count_params()}")
        
        # 训练模型 (注意参数顺序: 辅助特征作为price_input, 技术强度作为tech_input)
        history = model.train(
            X_auxiliary_train_scaled, X_tech_train_scaled, y_train_scaled,
            X_auxiliary_val_scaled, X_tech_val_scaled, y_val_scaled,
            epochs=15,
            batch_size=128
        )
        
        # 8. 模型评估
        print("\n模型评估...")
        
        # 在测试集上预测
        y_pred_scaled = model.predict(X_auxiliary_test_scaled, X_tech_test_scaled)
        
        # 反标准化预测结果
        y_pred = processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = processor.inverse_transform_target(y_test_scaled)
        
        # 计算准确率指标
        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        
        # 9. 结果展示
        print("\n" + "=" * 80)
        print("📊 以技术强度为主的挖掘结果")
        print("=" * 80)
        
        print(f"\n🎯 方向预测准确率:")
        print(f"   方向准确率: {direction_metrics['direction_accuracy']:.4f} ({direction_metrics['direction_accuracy']*100:.2f}%)")
        print(f"   精确率: {direction_metrics['precision']:.4f}")
        print(f"   召回率: {direction_metrics['recall']:.4f}")
        print(f"   F1分数: {direction_metrics['f1_score']:.4f}")
        
        print(f"\n📊 特征使用情况:")
        print(f"   技术强度特征数 (主要): {len(tech_features)}")
        print(f"   辅助特征数 (交易明细): {len(auxiliary_features)}")
        print(f"   总特征数: {len(tech_features) + len(auxiliary_features)}")
        
        print(f"\n📊 数据使用情况:")
        print(f"   训练样本: {len(X_tech_train)}")
        print(f"   测试样本: {len(X_tech_test)}")
        print(f"   序列长度: {processor.sequence_length}")
        
        # 分析预测分布
        print(f"\n📊 预测分布分析:")
        print(f"   真实涨跌分布: 上涨 {(y_test_original > 0).mean():.2%}, 下跌 {(y_test_original <= 0).mean():.2%}")
        print(f"   预测涨跌分布: 上涨 {(y_pred > 0).mean():.2%}, 下跌 {(y_pred <= 0).mean():.2%}")
        
        return True, direction_metrics['direction_accuracy']
        
    except Exception as e:
        print(f"❌ 技术强度主导测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    print("🚀 开始以技术强度为主的数据挖掘测试")
    
    start_time = datetime.now()
    success, accuracy = tech_strength_focused_test()
    end_time = datetime.now()
    
    if success:
        print(f"\n⏱️ 总耗时: {end_time - start_time}")
        print(f"\n🎉 技术强度主导测试完成!")
        print(f"   📍 最终准确率: {accuracy*100:.2f}%")
        
        # 与之前结果对比
        baseline_accuracy = 64.81
        improvement = (accuracy * 100) - baseline_accuracy
        print(f"\n📈 相比基础版本 ({baseline_accuracy:.2f}%):")
        if improvement > 0:
            print(f"   ✅ 提升了 {improvement:.2f} 个百分点!")
        else:
            print(f"   ⚠️ 下降了 {abs(improvement):.2f} 个百分点")
            
        print(f"\n💡 数据使用策略:")
        print(f"   🎯 主要数据: 技术强度指标 (从交易明细抽取)")
        print(f"   🔧 辅助数据: 股票交易明细 (基础交易数据)")
    else:
        print("❌ 技术强度主导测试失败")

if __name__ == "__main__":
    main()
