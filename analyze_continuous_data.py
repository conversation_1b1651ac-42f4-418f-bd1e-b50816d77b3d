#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析连续技术强度数据的真实范围和分布

作者: AI Assistant
日期: 2025-06-30
"""

import pandas as pd
import glob
import numpy as np

def analyze_continuous_data():
    """分析连续技术强度数据"""
    print("=" * 60)
    print("🔍 连续技术强度数据完整分析")
    print("=" * 60)
    
    # 加载所有技术强度数据文件
    tech_files = glob.glob('tech_strength/daily/*.xlsx')
    all_data = []
    
    print(f"加载 {len(tech_files)} 个数据文件...")
    for file in tech_files:
        df = pd.read_excel(file)
        all_data.append(df)
    
    tech_df = pd.concat(all_data, ignore_index=True)
    print(f"总数据量: {len(tech_df)} 条记录")
    
    # 分析连续技术强度指标
    continuous_cols = ['连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']
    
    for col in continuous_cols:
        if col in tech_df.columns:
            print(f"\n📊 {col}:")
            print(f"  最小值: {tech_df[col].min()}")
            print(f"  最大值: {tech_df[col].max()}")
            print(f"  平均值: {tech_df[col].mean():.2f}")
            print(f"  中位数: {tech_df[col].median()}")
            print(f"  唯一值数量: {tech_df[col].nunique()}")
            
            # 显示唯一值
            unique_vals = sorted(tech_df[col].unique())
            print(f"  唯一值: {unique_vals}")
            
            # 显示分布
            print(f"  数据分布:")
            value_counts = tech_df[col].value_counts().sort_index()
            for val, count in value_counts.items():
                percentage = count / len(tech_df) * 100
                print(f"    {val}: {count} 次 ({percentage:.2f}%)")
    
    # 检查是否三个指标相同
    print(f"\n🔍 三个连续指标的关系:")
    if all(col in tech_df.columns for col in continuous_cols):
        # 检查是否完全相同
        same_3_5 = (tech_df['连续技术强度3天数'] == tech_df['连续技术强度5天数']).all()
        same_5_10 = (tech_df['连续技术强度5天数'] == tech_df['连续技术强度10天数']).all()
        same_3_10 = (tech_df['连续技术强度3天数'] == tech_df['连续技术强度10天数']).all()
        
        print(f"  3天数 == 5天数: {same_3_5}")
        print(f"  5天数 == 10天数: {same_5_10}")
        print(f"  3天数 == 10天数: {same_3_10}")
        
        if same_3_5 and same_5_10:
            print("  ⚠️ 三个指标完全相同，可以只使用一个")
        else:
            print("  ✅ 三个指标有差异，应该分别处理")
    
    # 分析成交量倍数
    if '成交量是前一日几倍' in tech_df.columns:
        col = '成交量是前一日几倍'
        print(f"\n📊 {col}:")
        print(f"  最小值: {tech_df[col].min()}")
        print(f"  最大值: {tech_df[col].max()}")
        print(f"  平均值: {tech_df[col].mean():.2f}")
        print(f"  唯一值数量: {tech_df[col].nunique()}")
        
        unique_vals = sorted(tech_df[col].unique())
        print(f"  唯一值: {unique_vals}")
        
        print(f"  数据分布:")
        value_counts = tech_df[col].value_counts().sort_index()
        for val, count in value_counts.items():
            percentage = count / len(tech_df) * 100
            print(f"    {val}: {count} 次 ({percentage:.2f}%)")
    
    print(f"\n" + "=" * 60)
    print("📋 分析结论:")
    print("=" * 60)
    
    # 给出分段建议
    for col in continuous_cols:
        if col in tech_df.columns:
            min_val = tech_df[col].min()
            max_val = tech_df[col].max()
            unique_count = tech_df[col].nunique()
            
            print(f"\n{col}:")
            print(f"  范围: [{min_val}, {max_val}]")
            print(f"  唯一值: {unique_count} 个")
            
            if unique_count <= 10:
                print(f"  建议: 保持原始分类 (唯一值较少)")
            else:
                # 建议三等分
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                
                print(f"  建议三等分:")
                print(f"    低值段: [{min_val}, {cut1:.1f})")
                print(f"    中值段: [{cut1:.1f}, {cut2:.1f})")
                print(f"    高值段: [{cut2:.1f}, {max_val}]")
                
                # 计算每段的数据量
                low_count = ((tech_df[col] >= min_val) & (tech_df[col] < cut1)).sum()
                mid_count = ((tech_df[col] >= cut1) & (tech_df[col] < cut2)).sum()
                high_count = (tech_df[col] >= cut2).sum()
                
                print(f"    预计分布: 低({low_count}, {low_count/len(tech_df)*100:.1f}%) "
                      f"中({mid_count}, {mid_count/len(tech_df)*100:.1f}%) "
                      f"高({high_count}, {high_count/len(tech_df)*100:.1f}%)")

def main():
    analyze_continuous_data()

if __name__ == "__main__":
    main()
