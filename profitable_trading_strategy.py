#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于实际买卖规则的高胜率赚钱组合挖掘
买卖规则: 预测上涨当日开盘买入，次日开盘卖出

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class ProfitableTradingStrategy:
    """基于实际买卖规则的交易策略分析器"""
    
    def __init__(self, tech_strength_path, stock_data_path):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        
        # 高胜率组合
        self.profitable_combinations = []
    
    def load_and_merge_data(self):
        """加载并合并数据"""
        print("=" * 80)
        print("💰 基于实际买卖规则的高胜率赚钱组合挖掘")
        print("📋 买卖规则: 预测上涨当日开盘买入，次日开盘卖出")
        print("=" * 80)
        
        # 1. 加载技术强度数据
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        # 2. 加载股票交易明细
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"技术强度数据: {len(tech_df)} 条记录")
        print(f"股票交易明细: {len(stock_df)} 条记录")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 3. 合并数据
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='inner',  # 只保留两边都有的数据
            suffixes=('_tech', '_stock')
        )
        
        print(f"合并后数据: {len(merged_df)} 条记录")
        
        return merged_df
    
    def calculate_trading_returns(self, df):
        """计算基于实际买卖规则的交易收益"""
        print(f"\n💹 计算交易收益 (当日开盘买入，次日开盘卖出)...")
        
        # 按股票代码分组，计算次日开盘价
        trading_results = []
        
        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            for i in range(len(group) - 1):
                current_row = group.iloc[i]
                next_row = group.iloc[i + 1]
                
                # 检查是否为连续交易日
                current_date = current_row['日期']
                next_date = next_row['日期']
                
                # 当日开盘买入价格
                buy_price = current_row['开盘价']
                # 次日开盘卖出价格
                sell_price = next_row['开盘价']
                
                # 计算收益率
                if buy_price > 0 and sell_price > 0:
                    return_rate = (sell_price - buy_price) / buy_price
                    profit_amount = sell_price - buy_price
                    
                    # 保存交易记录
                    trading_record = {
                        '股票代码': stock_code,
                        '买入日期': current_date,
                        '卖出日期': next_date,
                        '买入价格': buy_price,
                        '卖出价格': sell_price,
                        '收益率': return_rate,
                        '盈利金额': profit_amount,
                        '是否盈利': return_rate > 0,
                        
                        # 技术指标
                        '技术强度': current_row['技术强度'],
                        '连续技术强度3天数': current_row.get('连续技术强度3天数', 0),
                        '连续技术强度5天数': current_row.get('连续技术强度5天数', 0),
                        '连续技术强度10天数': current_row.get('连续技术强度10天数', 0),
                        '成交量是前一日几倍': current_row.get('成交量是前一日几倍', 0),
                        '技术指标特征': current_row.get('技术指标特征', ''),
                        '趋势组合': current_row.get('趋势组合', ''),
                        '日内股票标记': current_row.get('日内股票标记', ''),
                        
                        # 价格指标
                        '当日最高价': current_row['最高价'],
                        '当日最低价': current_row['最低价'],
                        '当日收盘价': current_row.get('收盘价_stock', current_row.get('收盘价', 0)),
                        '当日成交量': current_row.get('成交量_stock', current_row.get('成交量', 0)),
                        '当日换手率': current_row.get('换手率', 0),
                        '当日涨跌幅': current_row.get('涨跌幅_stock', current_row.get('涨跌幅', 0)),
                    }
                    
                    trading_results.append(trading_record)
        
        trading_df = pd.DataFrame(trading_results)
        print(f"  生成交易记录: {len(trading_df)} 笔交易")
        
        if len(trading_df) > 0:
            # 整体统计
            total_trades = len(trading_df)
            profitable_trades = trading_df['是否盈利'].sum()
            win_rate = profitable_trades / total_trades
            avg_return = trading_df['收益率'].mean()
            avg_profit = trading_df['盈利金额'].mean()
            
            print(f"  总交易次数: {total_trades:,}")
            print(f"  盈利交易: {profitable_trades:,}")
            print(f"  胜率: {win_rate:.3f} ({win_rate*100:.1f}%)")
            print(f"  平均收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")
            print(f"  平均盈利金额: {avg_profit:.2f}元")
        
        return trading_df
    
    def prepare_features(self, df):
        """准备特征数据"""
        print(f"\n🔧 准备特征数据...")
        
        # 连续性指标分段
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500},
            '连续技术强度10天数': {'min': 28, 'max': 956}
        }
        
        for indicator, info in continuous_indicators.items():
            if indicator in df.columns:
                min_val = info['min']
                max_val = info['max']
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                
                df[f'{indicator}_分段'] = pd.cut(
                    df[indicator],
                    bins=[min_val - 0.1, cut1, cut2, max_val + 0.1],
                    labels=[1, 2, 3],
                    include_lowest=True
                ).astype(float)
        
        # 短中期组合特征
        if '连续技术强度3天数_分段' in df.columns and '连续技术强度5天数_分段' in df.columns:
            df['短中期组合'] = df['连续技术强度3天数_分段'] * 10 + df['连续技术强度5天数_分段']
        
        # 成交量分段
        if '成交量是前一日几倍' in df.columns:
            df['成交量分段'] = pd.cut(
                df['成交量是前一日几倍'],
                bins=[0, 1.0, 2.0, 4.0],
                labels=[1, 2, 3],
                include_lowest=True
            ).astype(float)
        
        return df
    
    def find_profitable_combinations(self, df, min_trades=50, min_win_rate=0.70, min_avg_return=0.01):
        """找到高胜率赚钱的特征组合"""
        print(f"\n🎯 寻找高胜率赚钱组合...")
        print(f"  筛选条件: 最少{min_trades}笔交易, 胜率≥{min_win_rate*100:.0f}%, 平均收益率≥{min_avg_return*100:.1f}%")
        
        profitable_combinations = []
        
        # 1. 单特征分析
        print(f"\n📋 单特征高胜率分析:")
        
        key_features = [
            '技术强度', '连续技术强度3天数_分段', '连续技术强度5天数_分段', 
            '短中期组合', '成交量是前一日几倍', '成交量分段'
        ]
        
        for feature in key_features:
            if feature in df.columns:
                print(f"\n  {feature}:")
                for value in sorted(df[feature].unique()):
                    if pd.notna(value):
                        subset = df[df[feature] == value]
                        if len(subset) >= min_trades:
                            win_rate = subset['是否盈利'].mean()
                            avg_return = subset['收益率'].mean()
                            avg_profit = subset['盈利金额'].mean()
                            total_trades = len(subset)
                            profitable_trades = subset['是否盈利'].sum()
                            
                            if win_rate >= min_win_rate and avg_return >= min_avg_return:
                                print(f"    ✅ {feature}={value}: 胜率{win_rate:.3f}({win_rate*100:.1f}%) "
                                      f"平均收益{avg_return:.4f}({avg_return*100:.2f}%) "
                                      f"交易{total_trades}笔 盈利{profitable_trades}笔")
                                
                                profitable_combinations.append({
                                    'features': {feature: value},
                                    'win_rate': win_rate,
                                    'avg_return': avg_return,
                                    'avg_profit': avg_profit,
                                    'total_trades': total_trades,
                                    'profitable_trades': profitable_trades,
                                    'description': f'{feature}={value}',
                                    'type': 'single'
                                })
                            elif total_trades >= 100:  # 显示大样本的结果
                                print(f"    📊 {feature}={value}: 胜率{win_rate:.3f}({win_rate*100:.1f}%) "
                                      f"平均收益{avg_return:.4f}({avg_return*100:.2f}%) "
                                      f"交易{total_trades}笔")
        
        # 2. 双特征组合分析
        print(f"\n📋 双特征组合高胜率分析:")
        
        feature_pairs = [
            ('技术强度', '成交量是前一日几倍'),
            ('技术强度', '连续技术强度3天数_分段'),
            ('技术强度', '连续技术强度5天数_分段'),
            ('技术强度', '短中期组合'),
            ('连续技术强度3天数_分段', '连续技术强度5天数_分段'),
            ('短中期组合', '成交量分段'),
        ]
        
        for feature1, feature2 in feature_pairs:
            if feature1 in df.columns and feature2 in df.columns:
                print(f"\n  {feature1} + {feature2}:")
                
                # 获取主要值
                values1 = sorted(df[feature1].unique())
                values2 = sorted(df[feature2].unique())
                
                # 限制组合数量
                if len(values1) > 8:
                    values1 = values1[:8]
                if len(values2) > 8:
                    values2 = values2[:8]
                
                combo_count = 0
                for val1 in values1:
                    for val2 in values2:
                        if pd.notna(val1) and pd.notna(val2):
                            subset = df[(df[feature1] == val1) & (df[feature2] == val2)]
                            if len(subset) >= min_trades:
                                win_rate = subset['是否盈利'].mean()
                                avg_return = subset['收益率'].mean()
                                avg_profit = subset['盈利金额'].mean()
                                total_trades = len(subset)
                                profitable_trades = subset['是否盈利'].sum()
                                
                                if win_rate >= min_win_rate and avg_return >= min_avg_return:
                                    print(f"    ✅ {feature1}={val1}, {feature2}={val2}: "
                                          f"胜率{win_rate:.3f}({win_rate*100:.1f}%) "
                                          f"平均收益{avg_return:.4f}({avg_return*100:.2f}%) "
                                          f"交易{total_trades}笔")
                                    
                                    profitable_combinations.append({
                                        'features': {feature1: val1, feature2: val2},
                                        'win_rate': win_rate,
                                        'avg_return': avg_return,
                                        'avg_profit': avg_profit,
                                        'total_trades': total_trades,
                                        'profitable_trades': profitable_trades,
                                        'description': f'{feature1}={val1}, {feature2}={val2}',
                                        'type': 'double'
                                    })
                                    combo_count += 1
                                    
                                    if combo_count >= 5:  # 限制输出数量
                                        break
                        if combo_count >= 5:
                            break
                    if combo_count >= 5:
                        break
        
        # 按综合评分排序 (胜率 * 平均收益率)
        for combo in profitable_combinations:
            combo['score'] = combo['win_rate'] * combo['avg_return']
        
        profitable_combinations.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"\n🎯 高胜率赚钱组合总结:")
        print(f"  发现 {len(profitable_combinations)} 个高胜率赚钱组合")
        
        if profitable_combinations:
            print(f"\n  前10个最佳组合:")
            for i, combo in enumerate(profitable_combinations[:10], 1):
                print(f"    {i:2d}. {combo['description']}")
                print(f"        胜率: {combo['win_rate']:.3f} ({combo['win_rate']*100:.1f}%)")
                print(f"        平均收益率: {combo['avg_return']:.4f} ({combo['avg_return']*100:.2f}%)")
                print(f"        平均盈利: {combo['avg_profit']:.2f}元")
                print(f"        交易次数: {combo['total_trades']} (盈利{combo['profitable_trades']}笔)")
                print(f"        综合评分: {combo['score']:.6f}")
                print()
        
        self.profitable_combinations = profitable_combinations
        return profitable_combinations
    
    def create_trading_rules(self, combinations):
        """基于高胜率组合创建交易规则"""
        print(f"\n📜 生成交易规则...")
        
        if not combinations:
            print("  ⚠️ 没有找到符合条件的高胜率组合")
            return []
        
        trading_rules = []
        
        for combo in combinations[:20]:  # 取前20个最佳组合
            rule = {
                'name': f"规则_{len(trading_rules)+1}",
                'description': combo['description'],
                'condition': self._create_condition_function(combo['features']),
                'expected_win_rate': combo['win_rate'],
                'expected_return': combo['avg_return'],
                'expected_profit': combo['avg_profit'],
                'historical_trades': combo['total_trades'],
                'confidence': self._calculate_confidence(combo)
            }
            trading_rules.append(rule)
        
        print(f"  生成 {len(trading_rules)} 条交易规则")
        return trading_rules
    
    def _create_condition_function(self, features):
        """创建条件判断函数"""
        def condition(row):
            for feature, value in features.items():
                if feature not in row or row[feature] != value:
                    return False
            return True
        return condition
    
    def _calculate_confidence(self, combo):
        """计算置信度"""
        # 基于胜率、收益率和交易次数计算置信度
        win_rate_score = combo['win_rate']
        return_score = min(combo['avg_return'] * 10, 1.0)  # 收益率权重
        volume_score = min(combo['total_trades'] / 1000, 1.0)  # 交易次数权重
        
        confidence = (win_rate_score * 0.5 + return_score * 0.3 + volume_score * 0.2)
        return min(confidence, 1.0)
    
    def save_results(self, combinations, output_path):
        """保存结果"""
        if not combinations:
            print("  ⚠️ 没有结果可保存")
            return
        
        # 转换为DataFrame
        results_data = []
        for combo in combinations:
            results_data.append({
                '组合描述': combo['description'],
                '胜率': f"{combo['win_rate']*100:.1f}%",
                '平均收益率': f"{combo['avg_return']*100:.2f}%",
                '平均盈利金额': f"{combo['avg_profit']:.2f}元",
                '总交易次数': combo['total_trades'],
                '盈利交易次数': combo['profitable_trades'],
                '综合评分': f"{combo['score']:.6f}",
                '组合类型': combo['type']
            })
        
        results_df = pd.DataFrame(results_data)
        results_df.to_excel(output_path, index=False)
        print(f"💾 高胜率组合结果已保存到: {output_path}")

def main():
    """主函数"""
    start_time = datetime.now()
    
    try:
        # 1. 初始化策略分析器
        strategy = ProfitableTradingStrategy(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily"
        )
        
        # 2. 加载和合并数据
        merged_df = strategy.load_and_merge_data()
        
        # 3. 计算交易收益
        trading_df = strategy.calculate_trading_returns(merged_df)
        
        if len(trading_df) == 0:
            print("❌ 没有有效的交易数据")
            return
        
        # 4. 准备特征
        trading_df = strategy.prepare_features(trading_df)
        
        # 5. 寻找高胜率赚钱组合
        profitable_combinations = strategy.find_profitable_combinations(
            trading_df, 
            min_trades=50,      # 最少50笔交易
            min_win_rate=0.70,  # 胜率≥70%
            min_avg_return=0.01 # 平均收益率≥1%
        )
        
        # 6. 创建交易规则
        trading_rules = strategy.create_trading_rules(profitable_combinations)
        
        # 7. 保存结果
        output_path = f"高胜率交易组合_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        strategy.save_results(profitable_combinations, output_path)
        
        end_time = datetime.now()
        
        print(f"\n" + "="*80)
        print(f"🎉 高胜率赚钱组合挖掘完成!")
        print(f"⏱️ 总耗时: {end_time - start_time}")
        print(f"📊 分析了 {len(trading_df):,} 笔实际交易")
        print(f"🎯 发现 {len(profitable_combinations)} 个高胜率组合")
        print(f"📜 生成 {len(trading_rules)} 条交易规则")
        print(f"💰 买卖规则: 当日开盘买入，次日开盘卖出")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
