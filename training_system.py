import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import tensorflow as tf
from tensorflow.keras.models import load_model
import os
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from integrated_data_processor import IntegratedStockDataProcessor
from multimodal_model import MultiModalStockPredictor
from feature_importance_analyzer import FeatureImportanceAnalyzer

class StockPredictionTrainingSystem:
    """股票预测训练系统"""
    
    def __init__(self, 
                 stock_data_path: str = "stock_data/daily",
                 tech_strength_path: str = "tech_strength/daily",
                 sequence_length: int = 30,
                 model_save_dir: str = "models"):
        """
        初始化训练系统
        
        Args:
            stock_data_path: 股票数据路径
            tech_strength_path: 技术强度数据路径
            sequence_length: 序列长度
            model_save_dir: 模型保存目录
        """
        self.stock_data_path = stock_data_path
        self.tech_strength_path = tech_strength_path
        self.sequence_length = sequence_length
        self.model_save_dir = model_save_dir
        
        # 创建保存目录
        os.makedirs(model_save_dir, exist_ok=True)
        
        # 初始化组件
        self.data_processor = IntegratedStockDataProcessor(
            stock_data_path=stock_data_path,
            tech_strength_path=tech_strength_path,
            sequence_length=sequence_length
        )
        
        self.model = None
        self.training_history = None
        self.feature_analyzer = FeatureImportanceAnalyzer()
        self.evaluation_results = None
        
    def prepare_data(self):
        """准备训练数据"""
        print("=" * 50)
        print("开始数据准备...")
        print("=" * 50)
        
        # 处理数据
        (self.X_price_train, self.X_price_test, 
         self.X_tech_train, self.X_tech_test, 
         self.y_train, self.y_test) = self.data_processor.process_integrated_data()
        
        print(f"数据准备完成!")
        print(f"价格特征维度: {self.X_price_train.shape[-1]}")
        print(f"技术强度特征维度: {self.X_tech_train.shape[-1]}")
        
        return True
    
    def build_and_compile_model(self, 
                               cnn_filters: list = [64, 128, 256],
                               lstm_units: list = [128, 64],
                               attention_heads: int = 8,
                               dropout_rate: float = 0.3,
                               learning_rate: float = 0.001):
        """构建和编译模型"""
        print("=" * 50)
        print("构建模型...")
        print("=" * 50)
        
        # 获取特征维度
        price_features = self.X_price_train.shape[-1]
        tech_features = self.X_tech_train.shape[-1]
        
        # 创建模型
        self.model = MultiModalStockPredictor(
            sequence_length=self.sequence_length,
            price_features=price_features,
            tech_features=tech_features,
            cnn_filters=cnn_filters,
            lstm_units=lstm_units,
            attention_heads=attention_heads,
            dropout_rate=dropout_rate
        )
        
        # 编译模型
        self.model.compile_model(learning_rate=learning_rate)
        
        # 显示模型结构
        print("模型结构:")
        self.model.get_model_summary()
        
        return self.model
    
    def train_model(self, 
                   epochs: int = 100,
                   batch_size: int = 32,
                   validation_split: float = 0.2,
                   early_stopping_patience: int = 15):
        """训练模型"""
        print("=" * 50)
        print("开始模型训练...")
        print("=" * 50)
        
        if self.model is None:
            raise ValueError("请先构建模型")
        
        # 生成模型保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_save_path = os.path.join(self.model_save_dir, f"stock_model_{timestamp}.h5")
        
        # 训练模型
        self.training_history = self.model.train(
            X_price_train=self.X_price_train,
            X_tech_train=self.X_tech_train,
            y_train=self.y_train,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            model_save_path=model_save_path
        )
        
        # 保存训练历史
        history_path = os.path.join(self.model_save_dir, f"training_history_{timestamp}.json")
        self.save_training_history(history_path)
        
        print(f"模型已保存到: {model_save_path}")
        print(f"训练历史已保存到: {history_path}")
        
        return self.training_history
    
    def evaluate_model(self):
        """评估模型"""
        print("=" * 50)
        print("评估模型性能...")
        print("=" * 50)
        
        if self.model is None:
            raise ValueError("请先训练模型")
        
        # 在测试集上评估
        self.evaluation_results = self.model.evaluate(
            self.X_price_test, self.X_tech_test, self.y_test
        )
        
        print("测试集评估结果:")
        for metric, value in self.evaluation_results.items():
            print(f"{metric}: {value:.6f}")
        
        # 预测结果
        y_pred = self.model.predict(self.X_price_test, self.X_tech_test)
        
        # 反标准化
        y_test_original = self.data_processor.inverse_transform_target(self.y_test)
        y_pred_original = self.data_processor.inverse_transform_target(y_pred)
        
        # 计算原始尺度的指标
        original_metrics = {
            'mse_original': mean_squared_error(y_test_original, y_pred_original),
            'mae_original': mean_absolute_error(y_test_original, y_pred_original),
            'r2_original': r2_score(y_test_original, y_pred_original),
            'direction_accuracy_original': np.mean(np.sign(y_test_original) == np.sign(y_pred_original))
        }
        
        print("\n原始尺度评估结果:")
        for metric, value in original_metrics.items():
            print(f"{metric}: {value:.6f}")
        
        # 合并评估结果
        self.evaluation_results.update(original_metrics)
        
        return self.evaluation_results, y_pred_original, y_test_original
    
    def plot_training_history(self, save_path: str = None):
        """绘制训练历史"""
        if self.training_history is None:
            print("没有训练历史可绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('模型训练历史', fontsize=16)
        
        # 损失函数
        axes[0, 0].plot(self.training_history.history['loss'], label='训练损失')
        axes[0, 0].plot(self.training_history.history['val_loss'], label='验证损失')
        axes[0, 0].set_title('损失函数')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # MAE
        axes[0, 1].plot(self.training_history.history['mae'], label='训练MAE')
        axes[0, 1].plot(self.training_history.history['val_mae'], label='验证MAE')
        axes[0, 1].set_title('平均绝对误差')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('MAE')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # MAPE
        axes[1, 0].plot(self.training_history.history['mape'], label='训练MAPE')
        axes[1, 0].plot(self.training_history.history['val_mape'], label='验证MAPE')
        axes[1, 0].set_title('平均绝对百分比误差')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('MAPE')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 学习率
        if 'lr' in self.training_history.history:
            axes[1, 1].plot(self.training_history.history['lr'], label='学习率')
            axes[1, 1].set_title('学习率变化')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Learning Rate')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        else:
            axes[1, 1].text(0.5, 0.5, '学习率数据不可用', ha='center', va='center', transform=axes[1, 1].transAxes)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练历史图已保存到: {save_path}")
        
        plt.show()
    
    def plot_prediction_results(self, y_pred, y_true, save_path: str = None, sample_size: int = 1000):
        """绘制预测结果"""
        # 随机采样以便可视化
        if len(y_pred) > sample_size:
            indices = np.random.choice(len(y_pred), sample_size, replace=False)
            y_pred_sample = y_pred[indices]
            y_true_sample = y_true[indices]
        else:
            y_pred_sample = y_pred
            y_true_sample = y_true
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('预测结果分析', fontsize=16)
        
        # 预测vs真实值散点图
        axes[0, 0].scatter(y_true_sample, y_pred_sample, alpha=0.6)
        axes[0, 0].plot([y_true_sample.min(), y_true_sample.max()], 
                       [y_true_sample.min(), y_true_sample.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('真实值')
        axes[0, 0].set_ylabel('预测值')
        axes[0, 0].set_title('预测值 vs 真实值')
        axes[0, 0].grid(True)
        
        # 残差图
        residuals = y_pred_sample - y_true_sample
        axes[0, 1].scatter(y_pred_sample, residuals, alpha=0.6)
        axes[0, 1].axhline(y=0, color='r', linestyle='--')
        axes[0, 1].set_xlabel('预测值')
        axes[0, 1].set_ylabel('残差')
        axes[0, 1].set_title('残差分析')
        axes[0, 1].grid(True)
        
        # 残差分布
        axes[1, 0].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 0].set_xlabel('残差')
        axes[1, 0].set_ylabel('频数')
        axes[1, 0].set_title('残差分布')
        axes[1, 0].grid(True)
        
        # 时间序列对比（取前200个点）
        n_points = min(200, len(y_pred_sample))
        axes[1, 1].plot(range(n_points), y_true_sample[:n_points], label='真实值', alpha=0.8)
        axes[1, 1].plot(range(n_points), y_pred_sample[:n_points], label='预测值', alpha=0.8)
        axes[1, 1].set_xlabel('时间点')
        axes[1, 1].set_ylabel('涨跌幅')
        axes[1, 1].set_title('时间序列对比')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"预测结果图已保存到: {save_path}")
        
        plt.show()
    
    def save_training_history(self, file_path: str):
        """保存训练历史"""
        if self.training_history is None:
            return
        
        # 转换为可序列化的格式
        history_dict = {}
        for key, values in self.training_history.history.items():
            history_dict[key] = [float(v) for v in values]
        
        with open(file_path, 'w') as f:
            json.dump(history_dict, f, indent=2)
    
    def save_evaluation_results(self, file_path: str):
        """保存评估结果"""
        if self.evaluation_results is None:
            return
        
        # 转换为可序列化的格式
        results_dict = {}
        for key, value in self.evaluation_results.items():
            results_dict[key] = float(value)
        
        with open(file_path, 'w') as f:
            json.dump(results_dict, f, indent=2)
    
    def run_complete_training(self, 
                            cnn_filters: list = [64, 128, 256],
                            lstm_units: list = [128, 64],
                            attention_heads: int = 8,
                            dropout_rate: float = 0.3,
                            learning_rate: float = 0.001,
                            epochs: int = 100,
                            batch_size: int = 32,
                            validation_split: float = 0.2):
        """运行完整的训练流程"""
        print("开始完整的股票预测模型训练流程...")
        
        # 1. 准备数据
        self.prepare_data()
        
        # 2. 构建模型
        self.build_and_compile_model(
            cnn_filters=cnn_filters,
            lstm_units=lstm_units,
            attention_heads=attention_heads,
            dropout_rate=dropout_rate,
            learning_rate=learning_rate
        )
        
        # 3. 训练模型
        self.train_model(
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split
        )
        
        # 4. 评估模型
        evaluation_results, y_pred, y_true = self.evaluate_model()
        
        # 5. 可视化结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 绘制训练历史
        history_plot_path = os.path.join(self.model_save_dir, f"training_history_{timestamp}.png")
        self.plot_training_history(save_path=history_plot_path)
        
        # 绘制预测结果
        prediction_plot_path = os.path.join(self.model_save_dir, f"prediction_results_{timestamp}.png")
        self.plot_prediction_results(y_pred, y_true, save_path=prediction_plot_path)
        
        # 6. 特征重要性分析
        print("正在进行特征重要性分析...")
        feature_importance_results = self.analyze_feature_importance()

        # 保存特征重要性分析结果
        feature_report_path = os.path.join(self.model_save_dir, f"feature_importance_report_{timestamp}.txt")
        self.feature_analyzer.generate_feature_report(feature_report_path)

        # 7. 保存评估结果
        results_path = os.path.join(self.model_save_dir, f"evaluation_results_{timestamp}.json")
        self.save_evaluation_results(results_path)

        print("=" * 50)
        print("训练流程完成!")
        print("=" * 50)
        print(f"模型文件保存在: {self.model_save_dir}")
        print(f"最终测试集方向准确率: {evaluation_results['direction_accuracy_original']:.4f}")
        print(f"最终测试集MAE: {evaluation_results['mae_original']:.6f}")
        print(f"最终测试集R²: {evaluation_results['r2_original']:.6f}")
        print(f"特征重要性分析报告: {feature_report_path}")

        # 合并结果
        evaluation_results['feature_importance'] = feature_importance_results

        return evaluation_results

    def analyze_feature_importance(self):
        """分析特征重要性"""
        if not hasattr(self, 'X_price_train') or not hasattr(self, 'X_tech_train'):
            print("警告: 训练数据不可用，跳过特征重要性分析")
            return {}

        try:
            # 进行特征重要性分析
            importance_results = self.feature_analyzer.analyze_feature_importance(
                self.X_price_train,
                self.X_tech_train,
                self.y_train,
                self.data_processor.price_feature_columns,
                self.data_processor.tech_feature_columns
            )

            # 选择最重要的特征
            top_features = self.feature_analyzer.select_top_features(top_k=100)

            print(f"特征重要性分析完成，识别出 {len(top_features)} 个重要特征")

            # 显示前10个最重要的特征
            combined_ranking = importance_results['combined_ranking']['feature_ranking']
            print("\n前10个最重要的特征:")
            for i, (feature, rank) in enumerate(combined_ranking[:10], 1):
                print(f"{i:2d}. {feature} (平均排名: {rank:.2f})")

            return importance_results

        except Exception as e:
            print(f"特征重要性分析出错: {e}")
            return {}
