#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
平衡准确率提升测试 - 逐步添加有效特征

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from integrated_data_processor import IntegratedStockDataProcessor
from multimodal_model import MultiModalStockPredictor
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)
    
    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)
    
    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def test_accuracy_improvements():
    """测试多种准确率提升策略"""
    print("=" * 80)
    print("🎯 平衡准确率提升测试")
    print("=" * 80)
    
    results_comparison = []
    
    # 策略1: 基础版本 (作为对比基准)
    #print("\n1️⃣ 基础版本测试...")
    #result1 = test_basic_version()
    #if result1:
        #results_comparison.append(("基础版本", result1))
    
    # 策略2: 增加技术指标
    #print("\n2️⃣ 增强技术指标版本...")
    #result2 = test_enhanced_technical_indicators()
    #if result2:
        #results_comparison.append(("增强技术指标", result2))
    
    # 策略3: 优化模型架构
    print("\n3️⃣ 优化模型架构版本...")
    result3 = test_optimized_architecture()
    if result3:
        results_comparison.append(("优化架构", result3))
    
    # 策略4: 集成学习
    print("\n4️⃣ 集成学习版本...")
    result4 = test_ensemble_approach()
    if result4:
        results_comparison.append(("集成学习", result4))
    
    # 结果对比
    print("\n" + "=" * 80)
    print("📊 准确率提升策略对比")
    print("=" * 80)
    
    for strategy, result in results_comparison:
        print(f"{strategy:15s}: {result['direction_accuracy']*100:.2f}% (特征数: {result.get('total_features', 'N/A')})")
    
    if results_comparison:
        best_strategy = max(results_comparison, key=lambda x: x[1]['direction_accuracy'])
        print(f"\n🏆 最佳策略: {best_strategy[0]} - {best_strategy[1]['direction_accuracy']*100:.2f}%")
        
        baseline = 64.81
        improvement = (best_strategy[1]['direction_accuracy'] * 100) - baseline
        print(f"📈 相比原始版本提升: {improvement:.2f} 个百分点")

def test_basic_version():
    """测试基础版本"""
    try:
        data_processor = IntegratedStockDataProcessor(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=20
        )
        
        # 加载和处理数据
        stock_df = data_processor.load_stock_data()
        tech_df = data_processor.load_tech_strength_data()
        merged_df = data_processor.merge_datasets(stock_df, tech_df)
        
        # 只使用基础特征
        merged_df = data_processor.create_price_features(merged_df)
        merged_df = data_processor.process_tech_strength_features(merged_df)
        
        # 基础特征
        basic_price_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '成交量', '成交额', '换手率',
            '价格变化率', '高低价差比', '开收价差比', 'SMA_5', 'SMA_20', 'RSI', 'MACD'
        ]
        
        basic_tech_features = [
            '技术强度', '技术强度_等级', '技术强度_normalized',
            '技术强度_极弱', '技术强度_弱', '技术强度_中等偏弱',
            '技术强度_中等偏强', '技术强度_强', '技术强度_极强'
        ]
        
        data_processor.price_feature_columns = [col for col in basic_price_features if col in merged_df.columns]
        data_processor.tech_feature_columns = [col for col in basic_tech_features if col in merged_df.columns]
        
        # 准备数据
        required_columns = (data_processor.price_feature_columns + 
                          data_processor.tech_feature_columns + 
                          [data_processor.target_column, '股票代码', '日期'])
        
        available_columns = [col for col in required_columns if col in merged_df.columns]
        merged_df = merged_df[available_columns].dropna()
        
        print(f"   基础版本数据: {len(merged_df)} 条记录")
        print(f"   特征数: {len(data_processor.price_feature_columns)} + {len(data_processor.tech_feature_columns)} = {len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns)}")
        
        # 创建序列和训练
        X_price, X_tech, y = data_processor.create_sequences(merged_df)
        
        # 简单分割
        n_samples = len(X_price)
        train_size = int(n_samples * 0.8)
        
        X_price_train, X_price_test = X_price[:train_size], X_price[train_size:]
        X_tech_train, X_tech_test = X_tech[:train_size], X_tech[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        # 标准化
        X_price_train_scaled, X_tech_train_scaled, y_train_scaled = data_processor.normalize_data(
            X_price_train, X_tech_train, y_train, fit_scalers=True
        )
        X_price_test_scaled, X_tech_test_scaled, y_test_scaled = data_processor.normalize_data(
            X_price_test, X_tech_test, y_test, fit_scalers=False
        )
        
        # 训练模型
        model = MultiModalStockPredictor(
            sequence_length=data_processor.sequence_length,
            price_features=X_price_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[64, 128],
            lstm_units=[128, 64],
            attention_heads=8,
            dropout_rate=0.3
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.001)
        
        # 快速训练
        history = model.train(
            X_price_train_scaled, X_tech_train_scaled, y_train_scaled,
            epochs=5,  # 快速测试
            batch_size=128
        )
        
        # 预测和评估
        y_pred_scaled = model.predict(X_price_test_scaled, X_tech_test_scaled)
        y_pred = data_processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = data_processor.inverse_transform_target(y_test_scaled)
        
        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        
        print(f"   基础版本准确率: {direction_metrics['direction_accuracy']*100:.2f}%")
        
        return {
            'direction_accuracy': direction_metrics['direction_accuracy'],
            'total_features': len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns),
            'test_samples': len(y_test)
        }
        
    except Exception as e:
        print(f"   基础版本测试失败: {e}")
        return None

def test_enhanced_technical_indicators():
    """测试增强技术指标版本"""
    try:
        data_processor = IntegratedStockDataProcessor(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=20
        )
        
        # 加载和处理数据
        stock_df = data_processor.load_stock_data()
        tech_df = data_processor.load_tech_strength_data()
        merged_df = data_processor.merge_datasets(stock_df, tech_df)
        
        # 创建增强技术指标
        merged_df = data_processor.create_price_features(merged_df)
        merged_df = data_processor.process_tech_strength_features(merged_df)
        
        # 添加一些安全的组合特征
        merged_df = data_processor.create_advanced_combination_features(merged_df)
        
        # 选择有效特征（避免过多NaN）
        enhanced_price_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '成交量', '成交额', '换手率',
            '价格变化率', '高低价差比', '开收价差比', 'SMA_5', 'SMA_20', 'RSI', 'MACD',
            'BB_upper', 'BB_lower', 'BB_position', 'volatility_5', 'volume_ratio_5'
        ]
        
        enhanced_tech_features = [
            '技术强度', '技术强度_等级', '技术强度_normalized',
            '技术强度_极弱', '技术强度_弱', '技术强度_中等偏弱',
            '技术强度_中等偏强', '技术强度_强', '技术强度_极强',
            '连续强度_3天_ratio', '连续强度_5天_ratio', '连续强度_10天_ratio',
            '技术强度加权收盘价', '技术强度加权成交量', '强度_价格变化交互'
        ]
        
        data_processor.price_feature_columns = [col for col in enhanced_price_features if col in merged_df.columns]
        data_processor.tech_feature_columns = [col for col in enhanced_tech_features if col in merged_df.columns]
        
        # 准备数据
        required_columns = (data_processor.price_feature_columns + 
                          data_processor.tech_feature_columns + 
                          [data_processor.target_column, '股票代码', '日期'])
        
        available_columns = [col for col in required_columns if col in merged_df.columns]
        merged_df = merged_df[available_columns].dropna()
        
        print(f"   增强技术指标数据: {len(merged_df)} 条记录")
        print(f"   特征数: {len(data_processor.price_feature_columns)} + {len(data_processor.tech_feature_columns)} = {len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns)}")
        
        if len(merged_df) < 10000:
            print("   数据太少，跳过此测试")
            return None
        
        # 其余步骤与基础版本相同
        X_price, X_tech, y = data_processor.create_sequences(merged_df)
        
        n_samples = len(X_price)
        train_size = int(n_samples * 0.8)
        
        X_price_train, X_price_test = X_price[:train_size], X_price[train_size:]
        X_tech_train, X_tech_test = X_tech[:train_size], X_tech[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        X_price_train_scaled, X_tech_train_scaled, y_train_scaled = data_processor.normalize_data(
            X_price_train, X_tech_train, y_train, fit_scalers=True
        )
        X_price_test_scaled, X_tech_test_scaled, y_test_scaled = data_processor.normalize_data(
            X_price_test, X_tech_test, y_test, fit_scalers=False
        )
        
        model = MultiModalStockPredictor(
            sequence_length=data_processor.sequence_length,
            price_features=X_price_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[64, 128],
            lstm_units=[128, 64],
            attention_heads=8,
            dropout_rate=0.3
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.001)
        
        history = model.train(
            X_price_train_scaled, X_tech_train_scaled, y_train_scaled,
            epochs=5,
            batch_size=128
        )
        
        y_pred_scaled = model.predict(X_price_test_scaled, X_tech_test_scaled)
        y_pred = data_processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = data_processor.inverse_transform_target(y_test_scaled)
        
        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        
        print(f"   增强技术指标准确率: {direction_metrics['direction_accuracy']*100:.2f}%")
        
        return {
            'direction_accuracy': direction_metrics['direction_accuracy'],
            'total_features': len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns),
            'test_samples': len(y_test)
        }
        
    except Exception as e:
        print(f"   增强技术指标测试失败: {e}")
        return None

def test_optimized_architecture():
    """测试优化模型架构版本"""
    try:
        # 使用与基础版本相同的数据，但优化模型架构
        data_processor = IntegratedStockDataProcessor(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=30  # 增加序列长度
        )
        
        stock_df = data_processor.load_stock_data()
        tech_df = data_processor.load_tech_strength_data()
        merged_df = data_processor.merge_datasets(stock_df, tech_df)
        
        merged_df = data_processor.create_price_features(merged_df)
        merged_df = data_processor.process_tech_strength_features(merged_df)
        
        # 使用基础特征但优化架构
        basic_price_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '成交量', '成交额', '换手率',
            '价格变化率', '高低价差比', '开收价差比', 'SMA_5', 'SMA_20', 'RSI', 'MACD'
        ]
        
        basic_tech_features = [
            '技术强度', '技术强度_等级', '技术强度_normalized',
            '技术强度_极弱', '技术强度_弱', '技术强度_中等偏弱',
            '技术强度_中等偏强', '技术强度_强', '技术强度_极强'
        ]
        
        data_processor.price_feature_columns = [col for col in basic_price_features if col in merged_df.columns]
        data_processor.tech_feature_columns = [col for col in basic_tech_features if col in merged_df.columns]
        
        required_columns = (data_processor.price_feature_columns + 
                          data_processor.tech_feature_columns + 
                          [data_processor.target_column, '股票代码', '日期'])
        
        available_columns = [col for col in required_columns if col in merged_df.columns]
        merged_df = merged_df[available_columns].dropna()
        
        print(f"   优化架构数据: {len(merged_df)} 条记录")
        print(f"   序列长度: {data_processor.sequence_length}")
        
        X_price, X_tech, y = data_processor.create_sequences(merged_df)
        
        n_samples = len(X_price)
        train_size = int(n_samples * 0.8)
        
        X_price_train, X_price_test = X_price[:train_size], X_price[train_size:]
        X_tech_train, X_tech_test = X_tech[:train_size], X_tech[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        X_price_train_scaled, X_tech_train_scaled, y_train_scaled = data_processor.normalize_data(
            X_price_train, X_tech_train, y_train, fit_scalers=True
        )
        X_price_test_scaled, X_tech_test_scaled, y_test_scaled = data_processor.normalize_data(
            X_price_test, X_tech_test, y_test, fit_scalers=False
        )
        
        # 优化的模型架构
        model = MultiModalStockPredictor(
            sequence_length=data_processor.sequence_length,
            price_features=X_price_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[128, 256, 128],  # 更深的CNN
            lstm_units=[256, 128, 64],    # 更深的LSTM
            attention_heads=16,           # 更多注意力头
            dropout_rate=0.4              # 更高dropout
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.0005)  # 更小学习率
        
        history = model.train(
            X_price_train_scaled, X_tech_train_scaled, y_train_scaled,
            epochs=8,  # 更多epoch
            batch_size=64   # 更小batch
        )
        
        y_pred_scaled = model.predict(X_price_test_scaled, X_tech_test_scaled)
        y_pred = data_processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = data_processor.inverse_transform_target(y_test_scaled)
        
        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        
        print(f"   优化架构准确率: {direction_metrics['direction_accuracy']*100:.2f}%")
        
        return {
            'direction_accuracy': direction_metrics['direction_accuracy'],
            'total_features': len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns),
            'test_samples': len(y_test)
        }
        
    except Exception as e:
        print(f"   优化架构测试失败: {e}")
        return None

def test_ensemble_approach():
    """测试集成学习版本"""
    try:
        # 简化的集成方法：训练多个模型并平均预测
        print("   训练集成模型...")
        
        # 使用基础数据
        data_processor = IntegratedStockDataProcessor(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=20
        )
        
        stock_df = data_processor.load_stock_data()
        tech_df = data_processor.load_tech_strength_data()
        merged_df = data_processor.merge_datasets(stock_df, tech_df)
        
        merged_df = data_processor.create_price_features(merged_df)
        merged_df = data_processor.process_tech_strength_features(merged_df)
        
        basic_price_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '成交量', '成交额', '换手率',
            '价格变化率', '高低价差比', '开收价差比', 'SMA_5', 'SMA_20', 'RSI', 'MACD'
        ]
        
        basic_tech_features = [
            '技术强度', '技术强度_等级', '技术强度_normalized',
            '技术强度_极弱', '技术强度_弱', '技术强度_中等偏弱',
            '技术强度_中等偏强', '技术强度_强', '技术强度_极强'
        ]
        
        data_processor.price_feature_columns = [col for col in basic_price_features if col in merged_df.columns]
        data_processor.tech_feature_columns = [col for col in basic_tech_features if col in merged_df.columns]
        
        required_columns = (data_processor.price_feature_columns + 
                          data_processor.tech_feature_columns + 
                          [data_processor.target_column, '股票代码', '日期'])
        
        available_columns = [col for col in required_columns if col in merged_df.columns]
        merged_df = merged_df[available_columns].dropna()
        
        X_price, X_tech, y = data_processor.create_sequences(merged_df)
        
        n_samples = len(X_price)
        train_size = int(n_samples * 0.8)
        
        X_price_train, X_price_test = X_price[:train_size], X_price[train_size:]
        X_tech_train, X_tech_test = X_tech[:train_size], X_tech[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        X_price_train_scaled, X_tech_train_scaled, y_train_scaled = data_processor.normalize_data(
            X_price_train, X_tech_train, y_train, fit_scalers=True
        )
        X_price_test_scaled, X_tech_test_scaled, y_test_scaled = data_processor.normalize_data(
            X_price_test, X_tech_test, y_test, fit_scalers=False
        )
        
        # 训练3个不同的模型
        models = []
        predictions = []
        
        for i in range(3):
            print(f"     训练模型 {i+1}/3...")
            
            model = MultiModalStockPredictor(
                sequence_length=data_processor.sequence_length,
                price_features=X_price_train.shape[-1],
                tech_features=X_tech_train.shape[-1],
                cnn_filters=[64, 128] if i == 0 else [128, 64] if i == 1 else [96, 96],
                lstm_units=[128, 64] if i == 0 else [64, 128] if i == 1 else [96, 96],
                attention_heads=8 if i == 0 else 4 if i == 1 else 12,
                dropout_rate=0.3 + i * 0.1
            )
            
            model.build_model()
            model.compile_model(learning_rate=0.001)
            
            # 快速训练
            history = model.train(
                X_price_train_scaled, X_tech_train_scaled, y_train_scaled,
                epochs=3,  # 快速训练
                batch_size=128
            )
            
            # 预测
            y_pred_scaled = model.predict(X_price_test_scaled, X_tech_test_scaled)
            y_pred = data_processor.inverse_transform_target(y_pred_scaled.flatten())
            
            predictions.append(y_pred)
            models.append(model)
        
        # 集成预测（简单平均）
        ensemble_pred = np.mean(predictions, axis=0)
        y_test_original = data_processor.inverse_transform_target(y_test_scaled)
        
        direction_metrics = calculate_direction_accuracy(y_test_original, ensemble_pred)
        
        print(f"   集成学习准确率: {direction_metrics['direction_accuracy']*100:.2f}%")
        
        return {
            'direction_accuracy': direction_metrics['direction_accuracy'],
            'total_features': len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns),
            'test_samples': len(y_test)
        }
        
    except Exception as e:
        print(f"   集成学习测试失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始平衡准确率提升测试")
    
    start_time = datetime.now()
    test_accuracy_improvements()
    end_time = datetime.now()
    
    print(f"\n⏱️ 总耗时: {end_time - start_time}")

if __name__ == "__main__":
    main()
