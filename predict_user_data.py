#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于高胜率规则预测用户数据的上涨概率
使用已验证的买卖规则：当日开盘买入，次日开盘卖出

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class UserDataPredictor:
    """用户数据预测器"""
    
    def __init__(self):
        # 基于验证的高胜率规则
        self.prediction_rules = [
            # 超级信号 (95%+胜率)
            {
                'name': '日内股票标记888',
                'condition': lambda row: row.get('日内股票标记', '') == '888',
                'probability': 0.950,
                'avg_return': 0.0842,
                'confidence': 'PERFECT',
                'description': '日内股票标记=888 (95.0%胜率，8.42%收益率)',
                'sample_count': 17015
            },
            {
                'name': '日内股票标记688',
                'condition': lambda row: row.get('日内股票标记', '') == '688',
                'probability': 0.939,
                'avg_return': 0.0447,
                'confidence': 'EXCELLENT',
                'description': '日内股票标记=688 (93.9%胜率，4.47%收益率)',
                'sample_count': 9209
            },
            
            # 技术强度100组合 (89-92%胜率)
            {
                'name': '技术强度100+成交量3.5倍',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 3.5),
                'probability': 0.891,
                'avg_return': 0.0661,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 + 成交量3.5倍 (89.1%胜率，6.61%收益率)',
                'sample_count': 2156
            },
            {
                'name': '技术强度100+成交量3.0倍',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 3.0),
                'probability': 0.912,
                'avg_return': 0.0543,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 + 成交量3.0倍 (91.2%胜率，5.43%收益率)',
                'sample_count': 1035
            },
            {
                'name': '技术强度100+成交量2.5倍',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 2.5),
                'probability': 0.924,
                'avg_return': 0.0504,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 + 成交量2.5倍 (92.4%胜率，5.04%收益率)',
                'sample_count': 1575
            },
            {
                'name': '技术强度100+成交量2.0倍',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 2.0),
                'probability': 0.921,
                'avg_return': 0.0430,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 + 成交量2.0倍 (92.1%胜率，4.30%收益率)',
                'sample_count': 3086
            },
            
            # 技术强度85组合 (84-89%胜率)
            {
                'name': '技术强度85+成交量3.5倍',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 3.5),
                'probability': 0.849,
                'avg_return': 0.0463,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 + 成交量3.5倍 (84.9%胜率，4.63%收益率)',
                'sample_count': 1948
            },
            {
                'name': '技术强度85+成交量3.0倍',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 3.0),
                'probability': 0.876,
                'avg_return': 0.0398,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 + 成交量3.0倍 (87.6%胜率，3.98%收益率)',
                'sample_count': 1082
            },
            {
                'name': '技术强度85+成交量2.5倍',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 2.5),
                'probability': 0.895,
                'avg_return': 0.0376,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 + 成交量2.5倍 (89.5%胜率，3.76%收益率)',
                'sample_count': 2044
            },
            
            # 单一技术强度规则
            {
                'name': '技术强度100',
                'condition': lambda row: row.get('技术强度', 0) == 100,
                'probability': 0.890,
                'avg_return': 0.0343,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=100 (89.0%胜率，3.43%收益率)',
                'sample_count': 30635
            },
            {
                'name': '技术强度85',
                'condition': lambda row: row.get('技术强度', 0) == 85,
                'probability': 0.813,
                'avg_return': 0.0203,
                'confidence': 'GOOD',
                'description': '技术强度=85 (81.3%胜率，2.03%收益率)',
                'sample_count': 12244
            },
            {
                'name': '技术强度71',
                'condition': lambda row: row.get('技术强度', 0) == 71,
                'probability': 0.744,
                'avg_return': 0.0074,
                'confidence': 'MODERATE',
                'description': '技术强度=71 (74.4%胜率，0.74%收益率)',
                'sample_count': 24489
            },
            
            # 成交量规则
            {
                'name': '成交量3.5倍',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 3.5,
                'probability': 0.786,
                'avg_return': 0.0402,
                'confidence': 'GOOD',
                'description': '成交量3.5倍 (78.6%胜率，4.02%收益率)',
                'sample_count': 6522
            },
            {
                'name': '成交量3.0倍',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 3.0,
                'probability': 0.808,
                'avg_return': 0.0340,
                'confidence': 'GOOD',
                'description': '成交量3.0倍 (80.8%胜率，3.40%收益率)',
                'sample_count': 4016
            },
            
            # 风险信号
            {
                'name': '技术强度28',
                'condition': lambda row: row.get('技术强度', 0) == 28,
                'probability': 0.331,
                'avg_return': -0.0138,
                'confidence': 'RISK',
                'description': '技术强度=28 (33.1%胜率，高风险)',
                'sample_count': 48979
            },
            {
                'name': '成交量0.5倍',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 0.5,
                'probability': 0.399,
                'avg_return': -0.0101,
                'confidence': 'RISK',
                'description': '成交量0.5倍 (39.9%胜率，低迷)',
                'sample_count': 65306
            }
        ]
    
    def predict_single_stock(self, row):
        """预测单只股票的上涨概率"""
        # 按优先级检查规则 (从高胜率到低胜率)
        for rule in self.prediction_rules:
            if rule['condition'](row):
                return {
                    'rule_name': rule['name'],
                    'probability': rule['probability'],
                    'probability_percent': rule['probability'] * 100,
                    'expected_return': rule['avg_return'],
                    'expected_return_percent': rule['avg_return'] * 100,
                    'confidence': rule['confidence'],
                    'description': rule['description'],
                    'sample_count': rule['sample_count']
                }
        
        # 如果没有匹配任何规则，返回基础概率
        return {
            'rule_name': '无匹配规则',
            'probability': 0.536,  # 整体胜率
            'probability_percent': 53.6,
            'expected_return': 0.0022,  # 整体平均收益率
            'expected_return_percent': 0.22,
            'confidence': 'NEUTRAL',
            'description': '无特定规则匹配，使用整体统计',
            'sample_count': 442761
        }
    
    def predict_user_data(self, file_path):
        """预测用户数据"""
        print("=" * 80)
        print("🔮 基于高胜率规则的股票上涨概率预测")
        print("📋 买卖规则: 当日开盘买入，次日开盘卖出")
        print("=" * 80)
        
        # 1. 加载数据
        print(f"📂 加载预测数据: {file_path}")
        try:
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                print(f"❌ 不支持的文件格式")
                return None
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return None
        
        print(f"  数据量: {len(df)} 只股票")
        
        # 2. 检查必要的列
        required_columns = ['股票代码']
        optional_columns = ['技术强度', '成交量是前一日几倍', '日内股票标记', '技术指标特征', '趋势组合']
        
        missing_required = [col for col in required_columns if col not in df.columns]
        if missing_required:
            print(f"❌ 缺少必要列: {missing_required}")
            return None
        
        available_optional = [col for col in optional_columns if col in df.columns]
        print(f"  可用预测特征: {available_optional}")
        
        # 3. 批量预测
        print(f"\n🎯 开始预测...")
        predictions = []
        
        for idx, row in df.iterrows():
            prediction = self.predict_single_stock(row)
            predictions.append(prediction)
        
        # 添加预测结果到DataFrame
        df['预测规则'] = [p['rule_name'] for p in predictions]
        df['上涨概率'] = [p['probability'] for p in predictions]
        df['上涨概率_百分比'] = [p['probability_percent'] for p in predictions]
        df['预期收益率'] = [p['expected_return'] for p in predictions]
        df['预期收益率_百分比'] = [p['expected_return_percent'] for p in predictions]
        df['置信等级'] = [p['confidence'] for p in predictions]
        df['预测依据'] = [p['description'] for p in predictions]
        df['历史样本数'] = [p['sample_count'] for p in predictions]
        
        return df, predictions
    
    def analyze_predictions(self, df, predictions):
        """分析预测结果"""
        print(f"\n📊 预测结果分析:")
        
        total = len(df)
        
        # 按置信等级分组
        confidence_groups = df['置信等级'].value_counts()
        print(f"\n  置信等级分布:")
        for confidence, count in confidence_groups.items():
            percentage = count / total * 100
            emoji = self._get_confidence_emoji(confidence)
            print(f"    {emoji} {confidence}: {count} 只 ({percentage:.1f}%)")
        
        # 按概率区间分组
        perfect = df[df['上涨概率'] >= 0.95]
        excellent = df[(df['上涨概率'] >= 0.85) & (df['上涨概率'] < 0.95)]
        very_good = df[(df['上涨概率'] >= 0.75) & (df['上涨概率'] < 0.85)]
        good = df[(df['上涨概率'] >= 0.65) & (df['上涨概率'] < 0.75)]
        moderate = df[(df['上涨概率'] >= 0.55) & (df['上涨概率'] < 0.65)]
        risk = df[df['上涨概率'] < 0.55]
        
        print(f"\n  概率分布:")
        print(f"    🎯 完美信号 (≥95%): {len(perfect)} 只 ({len(perfect)/total*100:.1f}%)")
        print(f"    🚀 优秀信号 (85-95%): {len(excellent)} 只 ({len(excellent)/total*100:.1f}%)")
        print(f"    ✅ 很好信号 (75-85%): {len(very_good)} 只 ({len(very_good)/total*100:.1f}%)")
        print(f"    📈 良好信号 (65-75%): {len(good)} 只 ({len(good)/total*100:.1f}%)")
        print(f"    ⚠️ 中性信号 (55-65%): {len(moderate)} 只 ({len(moderate)/total*100:.1f}%)")
        print(f"    📉 风险信号 (<55%): {len(risk)} 只 ({len(risk)/total*100:.1f}%)")
        
        # 平均概率
        avg_probability = df['上涨概率'].mean()
        avg_return = df['预期收益率'].mean()
        print(f"\n  平均上涨概率: {avg_probability:.3f} ({avg_probability*100:.1f}%)")
        print(f"  平均预期收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")
        
        return df
    
    def show_top_recommendations(self, df, top_n=20):
        """显示顶级推荐"""
        print(f"\n🏆 顶级推荐 (上涨概率最高的{top_n}只股票):")
        
        # 按上涨概率排序
        top_stocks = df.nlargest(top_n, '上涨概率')
        
        for idx, (_, row) in enumerate(top_stocks.iterrows(), 1):
            stock_code = row.get('股票代码', 'Unknown')
            stock_name = row.get('股票名称', '')
            probability = row['上涨概率_百分比']
            expected_return = row['预期收益率_百分比']
            confidence = row['置信等级']
            description = row['预测依据']
            tech_strength = row.get('技术强度', 'N/A')
            volume_ratio = row.get('成交量是前一日几倍', 'N/A')
            mark = row.get('日内股票标记', 'N/A')
            
            emoji = self._get_probability_emoji(probability)
            
            print(f"  {idx:2d}. {emoji} {stock_code} {stock_name}")
            print(f"      上涨概率: {probability:.1f}% | 预期收益: {expected_return:.2f}%")
            print(f"      预测依据: {description}")
            print(f"      技术强度: {tech_strength} | 成交量倍数: {volume_ratio} | 日内标记: {mark}")
            print(f"      置信等级: {confidence}")
            print()
    
    def _get_confidence_emoji(self, confidence):
        """获取置信等级emoji"""
        emoji_map = {
            'PERFECT': '🎯',
            'EXCELLENT': '🚀',
            'VERY_GOOD': '✅',
            'GOOD': '📈',
            'MODERATE': '👍',
            'NEUTRAL': '➡️',
            'RISK': '⚠️'
        }
        return emoji_map.get(confidence, '❓')
    
    def _get_probability_emoji(self, probability):
        """获取概率emoji"""
        if probability >= 95:
            return '🎯'
        elif probability >= 85:
            return '🚀'
        elif probability >= 75:
            return '✅'
        elif probability >= 65:
            return '📈'
        elif probability >= 55:
            return '👍'
        else:
            return '⚠️'
    
    def save_predictions(self, df, output_path):
        """保存预测结果"""
        # 选择要保存的列
        output_columns = [
            '股票代码', '股票名称', '日期',
            '上涨概率_百分比', '预期收益率_百分比', '置信等级', '预测依据',
            '技术强度', '成交量是前一日几倍', '日内股票标记',
            '历史样本数'
        ]
        
        available_columns = [col for col in output_columns if col in df.columns]
        
        # 重命名列
        column_rename = {
            '上涨概率_百分比': '明天上涨概率(%)',
            '预期收益率_百分比': '预期收益率(%)',
            '置信等级': '置信等级',
            '预测依据': '预测依据',
            '历史样本数': '历史验证样本数'
        }
        
        output_df = df[available_columns].copy()
        output_df = output_df.rename(columns=column_rename)
        
        # 按概率排序
        output_df = output_df.sort_values('明天上涨概率(%)', ascending=False)
        
        if output_path.endswith('.xlsx'):
            output_df.to_excel(output_path, index=False)
        else:
            output_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        print(f"💾 预测结果已保存到: {output_path}")

def main():
    """主函数"""
    predictor = UserDataPredictor()
    
    # 预测用户数据目录中的文件
    prediction_dir = "预测文件资料"
    
    if not os.path.exists(prediction_dir):
        print(f"❌ 预测数据目录不存在: {prediction_dir}")
        return
    
    # 获取目录中的所有Excel文件
    files = [f for f in os.listdir(prediction_dir) if f.endswith(('.xlsx', '.xls', '.csv'))]
    if not files:
        print(f"❌ 在 {prediction_dir} 目录中未找到数据文件")
        return
    
    print(f"📂 发现 {len(files)} 个数据文件:")
    for i, file in enumerate(files, 1):
        print(f"  {i}. {file}")
    
    # 处理每个文件
    for file_name in files:
        file_path = os.path.join(prediction_dir, file_name)
        
        print(f"\n" + "="*80)
        print(f"📊 预测文件: {file_name}")
        print(f"="*80)
        
        try:
            # 预测
            df, predictions = predictor.predict_user_data(file_path)
            
            if df is not None:
                # 分析结果
                df = predictor.analyze_predictions(df, predictions)
                
                # 显示顶级推荐
                predictor.show_top_recommendations(df, top_n=15)
                
                # 保存结果 - 修复文件后缀问题
                if file_path.endswith('.xlsx'):
                    output_path = file_path.replace('.xlsx', '_上涨概率预测.xlsx')
                elif file_path.endswith('.xls'):
                    output_path = file_path.replace('.xls', '_上涨概率预测.xlsx')
                elif file_path.endswith('.csv'):
                    output_path = file_path.replace('.csv', '_上涨概率预测.csv')
                else:
                    output_path = file_path + '_上涨概率预测.xlsx'
                predictor.save_predictions(df, output_path)
                
                print(f"✅ {file_name} 预测完成！")
            
        except Exception as e:
            print(f"❌ 预测 {file_name} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    print(f"\n🎉 所有文件预测完成！")
    print(f"📈 基于验证的高胜率规则进行预测")
    print(f"🎯 重点关注概率≥85%的股票")

if __name__ == "__main__":
    main()
