#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于高胜率特征组合的深度学习方案
先找到高准确率组合，再针对性训练模型

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_model import MultiModalStockPredictor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class HighAccuracyCombinationModel:
    """基于高胜率组合的深度学习模型"""

    def __init__(self, tech_strength_path, stock_data_path, sequence_length=20):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.sequence_length = sequence_length

        # 标准化器
        self.tech_scaler = StandardScaler()
        self.auxiliary_scaler = StandardScaler()
        self.target_scaler = StandardScaler()

        # 标签编码器
        self.label_encoders = {}

        # 高胜率组合
        self.high_accuracy_combinations = []

    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("=" * 80)
        print("🎯 基于高胜率特征组合的深度学习方案")
        print("=" * 80)

        # 1. 加载数据
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)

        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)

        print(f"技术强度数据: {len(tech_df)} 条记录")
        print(f"股票交易明细: {len(stock_df)} 条记录")

        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])

        # 2. 合并数据
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='left',
            suffixes=('_tech', '_stock')
        )

        print(f"合并后数据: {len(merged_df)} 条记录")

        # 3. 特征工程
        tech_strength_features = ['技术强度']

        # 连续性指标分段
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500}
        }

        for indicator, info in continuous_indicators.items():
            if indicator in merged_df.columns:
                min_val = info['min']
                max_val = info['max']
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3

                merged_df[f'{indicator}_分段'] = pd.cut(
                    merged_df[indicator],
                    bins=[min_val - 0.1, cut1, cut2, max_val + 0.1],
                    labels=[1, 2, 3],
                    include_lowest=True
                ).astype(float)

                tech_strength_features.append(f'{indicator}_分段')

        # 短中期组合特征
        if '连续技术强度3天数_分段' in merged_df.columns and '连续技术强度5天数_分段' in merged_df.columns:
            merged_df['短中期组合'] = merged_df['连续技术强度3天数_分段'] * 10 + merged_df['连续技术强度5天数_分段']
            merged_df['短中期差异'] = merged_df['连续技术强度5天数_分段'] - merged_df['连续技术强度3天数_分段']
            tech_strength_features.extend(['短中期组合', '短中期差异'])

        # 成交量特征
        if '成交量是前一日几倍' in merged_df.columns:
            tech_strength_features.append('成交量是前一日几倍')

        # 分类特征
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for col in categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')

                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))

                tech_strength_features.append(f'{col}_encoded')

        # 4. 辅助特征
        auxiliary_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价',
            '成交量_stock', '成交额', '换手率', '涨跌幅_stock'
        ]

        # 价格衍生特征
        if all(col in merged_df.columns for col in ['开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价']):
            merged_df['价格波动率'] = (merged_df['最高价'] - merged_df['最低价']) / merged_df['收盘价_stock']
            merged_df['开盘缺口'] = (merged_df['开盘价'] - merged_df['前收盘价']) / merged_df['前收盘价']
            price_range = merged_df['最高价'] - merged_df['最低价']
            price_range = price_range.replace(0, 1e-8)
            merged_df['收盘强度'] = (merged_df['收盘价_stock'] - merged_df['最低价']) / price_range

            auxiliary_features.extend(['价格波动率', '开盘缺口', '收盘强度'])

        # 辅助分类特征
        aux_categorical_features = ['复权状态', '交易状态', '是否ST股']
        for col in aux_categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')

                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))

                auxiliary_features.append(f'{col}_encoded')

        # 5. 确保特征列存在并处理缺失值
        available_tech_features = [col for col in tech_strength_features if col in merged_df.columns]
        available_auxiliary_features = [col for col in auxiliary_features if col in merged_df.columns]

        for col in available_tech_features + available_auxiliary_features:
            if merged_df[col].dtype in ['float64', 'int64']:
                merged_df[col] = merged_df[col].fillna(merged_df[col].median())

        # 6. 准备最终数据
        target_column = '涨跌幅_tech'
        feature_columns = available_tech_features + available_auxiliary_features + ['股票代码', '日期', target_column]
        final_df = merged_df[feature_columns].copy().dropna(subset=[target_column])

        print(f"\n📊 特征统计:")
        print(f"  技术强度特征: {len(available_tech_features)} 个")
        print(f"  辅助特征: {len(available_auxiliary_features)} 个")
        print(f"  总特征数: {len(available_tech_features) + len(available_auxiliary_features)}")
        print(f"  最终数据: {len(final_df)} 条记录")

        return final_df, available_tech_features, available_auxiliary_features, target_column

    def find_high_accuracy_combinations(self, df, target_column, min_samples=50, min_accuracy=0.75):
        """找到高准确率的特征组合"""
        print(f"\n🔍 寻找高准确率特征组合 (最小样本数: {min_samples}, 最小准确率: {min_accuracy*100:.0f}%)...")

        # 创建方向标签
        df['direction'] = (df[target_column] > 0).astype(int)

        high_accuracy_combinations = []

        # 1. 单特征分析
        print(f"\n📋 单特征高准确率分析:")

        key_features = ['技术强度', '连续技术强度3天数_分段', '连续技术强度5天数_分段',
                       '短中期组合', '成交量是前一日几倍', '技术指标特征_encoded', '趋势组合_encoded']

        for feature in key_features:
            if feature in df.columns:
                print(f"\n  {feature}:")
                for value in sorted(df[feature].unique()):
                    if pd.notna(value):
                        subset = df[df[feature] == value]
                        if len(subset) >= min_samples:
                            accuracy = subset['direction'].mean()
                            count = len(subset)

                            if accuracy >= min_accuracy:
                                print(f"    ✅ {feature}={value}: {accuracy:.3f} ({accuracy*100:.1f}%) - {count} 样本")
                                high_accuracy_combinations.append({
                                    'features': {feature: value},
                                    'accuracy': accuracy,
                                    'count': count,
                                    'description': f'{feature}={value}'
                                })
                            elif count >= 100:  # 显示大样本的结果
                                print(f"    📊 {feature}={value}: {accuracy:.3f} ({accuracy*100:.1f}%) - {count} 样本")

        # 2. 双特征组合分析
        print(f"\n📋 双特征组合高准确率分析:")

        feature_pairs = [
            ('技术强度', '连续技术强度3天数_分段'),
            ('技术强度', '连续技术强度5天数_分段'),
            ('连续技术强度3天数_分段', '连续技术强度5天数_分段'),
            ('技术强度', '短中期组合'),
            ('技术强度', '成交量是前一日几倍'),
            ('短中期组合', '成交量是前一日几倍'),
        ]

        for feature1, feature2 in feature_pairs:
            if feature1 in df.columns and feature2 in df.columns:
                print(f"\n  {feature1} + {feature2}:")

                # 获取主要值
                values1 = sorted(df[feature1].unique())
                values2 = sorted(df[feature2].unique())

                # 限制组合数量
                if len(values1) > 10:
                    values1 = values1[:10]
                if len(values2) > 10:
                    values2 = values2[:10]

                for val1 in values1:
                    for val2 in values2:
                        if pd.notna(val1) and pd.notna(val2):
                            subset = df[(df[feature1] == val1) & (df[feature2] == val2)]
                            if len(subset) >= min_samples:
                                accuracy = subset['direction'].mean()
                                count = len(subset)

                                if accuracy >= min_accuracy:
                                    print(f"    ✅ {feature1}={val1}, {feature2}={val2}: {accuracy:.3f} ({accuracy*100:.1f}%) - {count} 样本")
                                    high_accuracy_combinations.append({
                                        'features': {feature1: val1, feature2: val2},
                                        'accuracy': accuracy,
                                        'count': count,
                                        'description': f'{feature1}={val1}, {feature2}={val2}'
                                    })

        # 3. 三特征组合分析 (只分析最有希望的组合)
        print(f"\n📋 三特征组合高准确率分析:")

        triple_features = [
            ('技术强度', '连续技术强度3天数_分段', '连续技术强度5天数_分段'),
            ('技术强度', '短中期组合', '成交量是前一日几倍'),
        ]

        for feature1, feature2, feature3 in triple_features:
            if all(f in df.columns for f in [feature1, feature2, feature3]):
                print(f"\n  {feature1} + {feature2} + {feature3}:")

                # 只分析主要值的组合
                values1 = sorted(df[feature1].unique())[:5]
                values2 = sorted(df[feature2].unique())[:5]
                values3 = sorted(df[feature3].unique())[:5]

                combo_count = 0
                for val1 in values1:
                    for val2 in values2:
                        for val3 in values3:
                            if pd.notna(val1) and pd.notna(val2) and pd.notna(val3):
                                subset = df[(df[feature1] == val1) & (df[feature2] == val2) & (df[feature3] == val3)]
                                if len(subset) >= min_samples:
                                    accuracy = subset['direction'].mean()
                                    count = len(subset)

                                    if accuracy >= min_accuracy:
                                        print(f"    ✅ {feature1}={val1}, {feature2}={val2}, {feature3}={val3}: {accuracy:.3f} ({accuracy*100:.1f}%) - {count} 样本")
                                        high_accuracy_combinations.append({
                                            'features': {feature1: val1, feature2: val2, feature3: val3},
                                            'accuracy': accuracy,
                                            'count': count,
                                            'description': f'{feature1}={val1}, {feature2}={val2}, {feature3}={val3}'
                                        })
                                        combo_count += 1

                                        if combo_count >= 5:  # 限制输出数量
                                            break
                            if combo_count >= 5:
                                break
                        if combo_count >= 5:
                            break

        # 按准确率排序
        high_accuracy_combinations.sort(key=lambda x: x['accuracy'], reverse=True)

        print(f"\n🎯 高准确率组合总结:")
        print(f"  发现 {len(high_accuracy_combinations)} 个高准确率组合")

        if high_accuracy_combinations:
            print(f"\n  前10个最佳组合:")
            for i, combo in enumerate(high_accuracy_combinations[:10], 1):
                print(f"    {i:2d}. {combo['description']}")
                print(f"        准确率: {combo['accuracy']:.3f} ({combo['accuracy']*100:.1f}%)")
                print(f"        样本数: {combo['count']}")

        self.high_accuracy_combinations = high_accuracy_combinations
        return high_accuracy_combinations

    def create_high_accuracy_dataset(self, df, tech_features, auxiliary_features, target_column, combinations):
        """基于高准确率组合创建训练数据集"""
        print(f"\n🎯 基于高准确率组合创建数据集...")

        if not combinations:
            print("  ⚠️ 没有找到高准确率组合，使用全部数据")
            return self.create_sequences(df, tech_features, auxiliary_features, target_column)

        # 创建高准确率样本的掩码
        high_accuracy_mask = pd.Series(False, index=df.index)

        for combo in combinations:
            # 创建当前组合的掩码
            combo_mask = pd.Series(True, index=df.index)
            for feature, value in combo['features'].items():
                if feature in df.columns:
                    combo_mask &= (df[feature] == value)

            high_accuracy_mask |= combo_mask
            print(f"  添加组合: {combo['description']} - {combo_mask.sum()} 样本")

        # 筛选高准确率数据
        high_accuracy_df = df[high_accuracy_mask].copy()
        print(f"  高准确率数据: {len(high_accuracy_df)} 条记录 ({len(high_accuracy_df)/len(df)*100:.1f}%)")

        # 为了保证足够的训练数据，如果高准确率数据太少，补充一些中等准确率数据
        if len(high_accuracy_df) < 10000:
            print(f"  高准确率数据较少，补充中等准确率数据...")

            # 随机采样剩余数据
            remaining_df = df[~high_accuracy_mask]
            if len(remaining_df) > 0:
                sample_size = min(20000 - len(high_accuracy_df), len(remaining_df))
                sampled_df = remaining_df.sample(n=sample_size, random_state=42)

                # 合并数据
                combined_df = pd.concat([high_accuracy_df, sampled_df], ignore_index=True)
                print(f"  补充数据: {len(sampled_df)} 条记录")
                print(f"  最终数据: {len(combined_df)} 条记录")

                return self.create_sequences(combined_df, tech_features, auxiliary_features, target_column)

        return self.create_sequences(high_accuracy_df, tech_features, auxiliary_features, target_column)

    def create_sequences(self, df, tech_features, auxiliary_features, target_column):
        """创建时间序列"""
        print(f"\n⏰ 创建时间序列 (序列长度: {self.sequence_length})...")

        X_tech_list = []
        X_auxiliary_list = []
        y_list = []

        for _, group in df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)

            if len(group) < self.sequence_length + 1:
                continue

            tech_data = group[tech_features].values
            auxiliary_data = group[auxiliary_features].values
            target_data = group[target_column].values

            for i in range(len(group) - self.sequence_length):
                X_tech_list.append(tech_data[i:i+self.sequence_length])
                X_auxiliary_list.append(auxiliary_data[i:i+self.sequence_length])
                y_list.append(target_data[i+self.sequence_length])

        X_tech = np.array(X_tech_list)
        X_auxiliary = np.array(X_auxiliary_list)
        y = np.array(y_list)

        print(f"  创建序列: {len(X_tech)} 个样本")
        print(f"  技术强度特征形状: {X_tech.shape}")
        print(f"  辅助特征形状: {X_auxiliary.shape}")

        return X_tech, X_auxiliary, y

    def normalize_data(self, X_tech, X_auxiliary, y, fit_scalers=False):
        """标准化数据"""
        if fit_scalers:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])

            X_tech_scaled = self.tech_scaler.fit_transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.fit_transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])

            X_tech_scaled = self.tech_scaler.transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.transform(y.reshape(-1, 1)).flatten()

        X_tech_scaled = X_tech_scaled.reshape(X_tech.shape)
        X_auxiliary_scaled = X_auxiliary_scaled.reshape(X_auxiliary.shape)

        return X_tech_scaled, X_auxiliary_scaled, y_scaled

    def inverse_transform_target(self, y_scaled):
        """反标准化目标变量"""
        return self.target_scaler.inverse_transform(y_scaled.reshape(-1, 1)).flatten()

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    if len(y_true) == 0:
        return {'direction_accuracy': 0, 'precision': 0, 'recall': 0, 'f1_score': 0}

    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)

    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)

    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def high_accuracy_combination_test():
    """基于高胜率组合的深度学习测试"""
    try:
        # 1. 初始化模型
        model_processor = HighAccuracyCombinationModel(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily",
            sequence_length=20
        )

        # 2. 加载和准备数据
        final_df, tech_features, auxiliary_features, target_column = model_processor.load_and_prepare_data()

        # 3. 寻找高准确率组合
        high_accuracy_combinations = model_processor.find_high_accuracy_combinations(
            final_df, target_column, min_samples=50, min_accuracy=0.75
        )

        if not high_accuracy_combinations:
            print("⚠️ 未找到高准确率组合，降低标准重新搜索...")
            high_accuracy_combinations = model_processor.find_high_accuracy_combinations(
                final_df, target_column, min_samples=30, min_accuracy=0.70
            )

        # 4. 基于高准确率组合创建数据集
        X_tech, X_auxiliary, y = model_processor.create_high_accuracy_dataset(
            final_df, tech_features, auxiliary_features, target_column, high_accuracy_combinations
        )

        # 5. 数据分割
        print(f"\n📊 数据分割...")
        n_samples = len(X_tech)
        train_size = int(n_samples * 0.7)
        val_size = int(n_samples * 0.15)

        X_tech_train = X_tech[:train_size]
        X_auxiliary_train = X_auxiliary[:train_size]
        y_train = y[:train_size]

        X_tech_val = X_tech[train_size:train_size+val_size]
        X_auxiliary_val = X_auxiliary[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]

        X_tech_test = X_tech[train_size+val_size:]
        X_auxiliary_test = X_auxiliary[train_size+val_size:]
        y_test = y[train_size+val_size:]

        print(f"  训练集: {len(X_tech_train)} 样本")
        print(f"  验证集: {len(X_tech_val)} 样本")
        print(f"  测试集: {len(X_tech_test)} 样本")

        # 6. 数据标准化
        print(f"\n🔧 数据标准化...")
        X_tech_train_scaled, X_auxiliary_train_scaled, y_train_scaled = model_processor.normalize_data(
            X_tech_train, X_auxiliary_train, y_train, fit_scalers=True
        )
        X_tech_val_scaled, X_auxiliary_val_scaled, y_val_scaled = model_processor.normalize_data(
            X_tech_val, X_auxiliary_val, y_val, fit_scalers=False
        )
        X_tech_test_scaled, X_auxiliary_test_scaled, y_test_scaled = model_processor.normalize_data(
            X_tech_test, X_auxiliary_test, y_test, fit_scalers=False
        )

        # 7. 创建和训练专门的深度学习模型
        print(f"\n🏗️ 创建专门针对高胜率组合的深度学习模型...")

        # 针对高胜率数据的特殊模型架构
        model = MultiModalStockPredictor(
            sequence_length=model_processor.sequence_length,
            price_features=X_auxiliary_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[256, 512, 256],  # 更深的CNN用于捕获复杂模式
            lstm_units=[512, 256],        # 更大的LSTM
            attention_heads=32,           # 更多注意力头
            dropout_rate=0.3              # 适中的dropout
        )

        model.build_model()
        model.compile_model(learning_rate=0.0003)  # 更小的学习率

        print(f"  模型参数量: {model.model.count_params()}")

        # 训练模型
        print(f"\n🚀 训练高胜率组合专用模型...")
        model.train(
            X_auxiliary_train_scaled, X_tech_train_scaled, y_train_scaled,
            X_auxiliary_val_scaled, X_tech_val_scaled, y_val_scaled,
            epochs=20,  # 更多epoch
            batch_size=32   # 更小的batch size
        )

        # 8. 模型评估
        print(f"\n📈 模型评估...")

        y_pred_scaled = model.predict(X_auxiliary_test_scaled, X_tech_test_scaled)
        y_pred = model_processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = model_processor.inverse_transform_target(y_test_scaled)

        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)

        # 9. 结果展示
        print("\n" + "=" * 80)
        print("📊 基于高胜率组合的深度学习结果")
        print("=" * 80)

        print(f"\n🎯 方向预测准确率:")
        print(f"   方向准确率: {direction_metrics['direction_accuracy']:.4f} ({direction_metrics['direction_accuracy']*100:.2f}%)")
        print(f"   精确率: {direction_metrics['precision']:.4f}")
        print(f"   召回率: {direction_metrics['recall']:.4f}")
        print(f"   F1分数: {direction_metrics['f1_score']:.4f}")

        print(f"\n📊 高胜率组合统计:")
        print(f"   发现高胜率组合: {len(high_accuracy_combinations)} 个")
        if high_accuracy_combinations:
            print(f"   最高准确率组合: {high_accuracy_combinations[0]['accuracy']*100:.1f}%")
            print(f"   平均准确率: {np.mean([c['accuracy'] for c in high_accuracy_combinations])*100:.1f}%")

        print(f"\n📊 数据使用策略:")
        print(f"   ✅ 基于高胜率特征组合筛选数据")
        print(f"   ✅ 专门针对高胜率模式训练模型")
        print(f"   ✅ 使用更深的网络架构")
        print(f"   ✅ 优化的训练策略")

        return True, direction_metrics['direction_accuracy']

    except Exception as e:
        print(f"❌ 高胜率组合深度学习测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    start_time = datetime.now()
    success, accuracy = high_accuracy_combination_test()
    end_time = datetime.now()

    if success:
        print(f"\n⏱️ 总耗时: {end_time - start_time}")
        print(f"\n🎉 高胜率组合深度学习完成!")
        print(f"   📍 最终准确率: {accuracy*100:.2f}%")

        baseline_accuracy = 64.81
        previous_best = 66.62
        improvement_vs_baseline = (accuracy * 100) - baseline_accuracy
        improvement_vs_previous = (accuracy * 100) - previous_best

        print(f"\n📈 准确率对比:")
        print(f"   基础版本: {baseline_accuracy:.2f}%")
        print(f"   之前最佳: {previous_best:.2f}%")
        print(f"   高胜率组合版本: {accuracy*100:.2f}%")

        if improvement_vs_baseline > 0:
            print(f"   ✅ 相比基础版本提升: {improvement_vs_baseline:.2f} 个百分点!")

        if improvement_vs_previous > 0:
            print(f"   ✅ 相比之前最佳提升: {improvement_vs_previous:.2f} 个百分点!")
            print(f"   🚀 高胜率组合策略成功!")
        else:
            print(f"   ⚠️ 相比之前最佳下降: {abs(improvement_vs_previous):.2f} 个百分点")
            print(f"   💡 可能需要调整组合筛选策略")

        if accuracy > 0.70:
            print(f"\n🎊 恭喜！准确率超过70%，这是一个优秀的结果!")
        elif accuracy > 0.68:
            print(f"\n🎯 很好！准确率超过68%，接近优秀水平!")

    else:
        print("❌ 高胜率组合深度学习测试失败")

if __name__ == "__main__":
    main()