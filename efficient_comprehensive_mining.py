#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高效全面的策略挖掘系统
更全面的特征组合探索，优化性能

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EfficientComprehensiveMining:
    """高效全面的策略挖掘系统"""
    
    def __init__(self, tech_strength_path, stock_data_path):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.all_results = []
    
    def load_and_process_data(self):
        """加载并处理数据"""
        print("=" * 80)
        print("🔍 高效全面的策略挖掘系统")
        print("📋 买卖规则: 当日开盘买入，次日开盘卖出")
        print("=" * 80)
        
        # 加载数据
        print("📂 加载数据...")
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"  技术强度数据: {len(tech_df)} 条")
        print(f"  股票交易数据: {len(stock_df)} 条")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 合并数据
        print("🔗 合并数据...")
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='inner',
            suffixes=('_tech', '_stock')
        )
        
        print(f"  合并后数据: {len(merged_df)} 条")
        
        # 计算交易收益
        print("💹 计算交易收益...")
        trading_results = []
        
        for stock_code, group in merged_df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            for i in range(len(group) - 1):
                current_row = group.iloc[i]
                next_row = group.iloc[i + 1]
                
                buy_price = current_row['开盘价']
                sell_price = next_row['开盘价']
                
                if buy_price > 0 and sell_price > 0:
                    return_rate = (sell_price - buy_price) / buy_price
                    
                    trading_record = {
                        '股票代码': stock_code,
                        '收益率': return_rate,
                        '是否盈利': return_rate > 0,
                        
                        # 核心技术指标
                        '技术强度': current_row['技术强度'],
                        '连续技术强度3天数': current_row.get('连续技术强度3天数', 0),
                        '连续技术强度5天数': current_row.get('连续技术强度5天数', 0),
                        '连续技术强度10天数': current_row.get('连续技术强度10天数', 0),
                        '成交量是前一日几倍': current_row.get('成交量是前一日几倍', 0),
                        '技术指标特征': current_row.get('技术指标特征', ''),
                        '趋势组合': current_row.get('趋势组合', ''),
                        
                        # 价格指标
                        '开盘价': current_row['开盘价'],
                        '最高价': current_row['最高价'],
                        '最低价': current_row['最低价'],
                        '收盘价': current_row.get('收盘价_stock', current_row.get('收盘价', 0)),
                        '换手率': current_row.get('换手率', 0),
                        '涨跌幅': current_row.get('涨跌幅_stock', current_row.get('涨跌幅', 0)),
                    }
                    
                    trading_results.append(trading_record)
        
        trading_df = pd.DataFrame(trading_results)
        print(f"  生成交易记录: {len(trading_df)} 笔")
        
        if len(trading_df) > 0:
            win_rate = trading_df['是否盈利'].mean()
            avg_return = trading_df['收益率'].mean()
            print(f"  整体胜率: {win_rate:.3f} ({win_rate*100:.1f}%)")
            print(f"  平均收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")
        
        return trading_df
    
    def create_enhanced_features(self, df):
        """创建增强特征"""
        print("\n🔧 创建增强特征...")
        
        # 1. 技术强度多种分段
        if '技术强度' in df.columns:
            # 基于实际值的分段
            df['技术强度_二分'] = (df['技术强度'] >= 57).astype(int)  # 0: 弱, 1: 强
            df['技术强度_三分'] = pd.cut(df['技术强度'], bins=[27, 42, 71, 101], labels=[1, 2, 3]).astype(float)
            df['技术强度_五分'] = pd.cut(df['技术强度'], bins=[27, 35, 50, 65, 85, 101], labels=[1, 2, 3, 4, 5]).astype(float)
            
            # 极值标记
            df['技术强度_极低'] = (df['技术强度'] == 28).astype(int)
            df['技术强度_极高'] = (df['技术强度'] == 100).astype(int)
            df['技术强度_高强'] = (df['技术强度'] >= 85).astype(int)
        
        # 2. 连续技术强度分段
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500},
            '连续技术强度10天数': {'min': 28, 'max': 956}
        }
        
        for indicator, info in continuous_indicators.items():
            if indicator in df.columns:
                min_val, max_val = info['min'], info['max']
                range_size = max_val - min_val
                
                # 三等分
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                df[f'{indicator}_三分'] = pd.cut(
                    df[indicator], 
                    bins=[min_val-1, cut1, cut2, max_val+1], 
                    labels=[1, 2, 3]
                ).astype(float)
                
                # 五等分
                cuts = [min_val-1] + [min_val + i*range_size/5 for i in range(1, 5)] + [max_val+1]
                df[f'{indicator}_五分'] = pd.cut(
                    df[indicator], bins=cuts, labels=[1, 2, 3, 4, 5]
                ).astype(float)
        
        # 3. 成交量多种分段
        if '成交量是前一日几倍' in df.columns:
            df['成交量_二分'] = (df['成交量是前一日几倍'] >= 1.5).astype(int)
            df['成交量_三分'] = pd.cut(
                df['成交量是前一日几倍'], 
                bins=[0, 1.0, 2.0, 4.0], 
                labels=[1, 2, 3]
            ).astype(float)
            df['成交量_五分'] = pd.cut(
                df['成交量是前一日几倍'], 
                bins=[0, 0.8, 1.2, 1.8, 2.5, 4.0], 
                labels=[1, 2, 3, 4, 5]
            ).astype(float)
            
            # 极值标记
            df['成交量_极低'] = (df['成交量是前一日几倍'] == 0.5).astype(int)
            df['成交量_极高'] = (df['成交量是前一日几倍'] >= 3.0).astype(int)
            df['成交量_超高'] = (df['成交量是前一日几倍'] >= 3.5).astype(int)
        
        # 4. 价格相关特征
        if all(col in df.columns for col in ['开盘价', '最高价', '最低价', '收盘价']):
            # 价格波动率
            df['价格波动率'] = (df['最高价'] - df['最低价']) / df['收盘价']
            df['高波动'] = (df['价格波动率'] > df['价格波动率'].quantile(0.8)).astype(int)
            
            # 收盘强度
            price_range = df['最高价'] - df['最低价']
            price_range = price_range.replace(0, 1e-8)
            df['收盘强度'] = (df['收盘价'] - df['最低价']) / price_range
            df['强势收盘'] = (df['收盘强度'] > 0.7).astype(int)
        
        # 5. 换手率分段
        if '换手率' in df.columns:
            df['高换手'] = (df['换手率'] > df['换手率'].quantile(0.8)).astype(int)
        
        # 6. 组合特征
        if '连续技术强度3天数_三分' in df.columns and '连续技术强度5天数_三分' in df.columns:
            df['短中期组合'] = df['连续技术强度3天数_三分'] * 10 + df['连续技术强度5天数_三分']
        
        if '技术强度_三分' in df.columns and '成交量_三分' in df.columns:
            df['技术成交量组合'] = df['技术强度_三分'] * 10 + df['成交量_三分']
        
        print(f"  特征创建完成，当前特征数: {len(df.columns)}")
        return df
    
    def comprehensive_analysis(self, df, min_trades=20, min_win_rate=0.65):
        """全面分析"""
        print(f"\n🎯 全面策略分析 (最少{min_trades}笔交易, 胜率≥{min_win_rate*100:.0f}%)...")
        
        all_results = []
        
        # 定义要分析的特征
        analysis_features = [
            # 技术强度相关
            '技术强度', '技术强度_二分', '技术强度_三分', '技术强度_五分',
            '技术强度_极低', '技术强度_极高', '技术强度_高强',
            
            # 连续技术强度
            '连续技术强度3天数_三分', '连续技术强度3天数_五分',
            '连续技术强度5天数_三分', '连续技术强度5天数_五分',
            '连续技术强度10天数_三分', '连续技术强度10天数_五分',
            
            # 成交量相关
            '成交量是前一日几倍', '成交量_二分', '成交量_三分', '成交量_五分',
            '成交量_极低', '成交量_极高', '成交量_超高',
            
            # 价格相关
            '高波动', '强势收盘', '高换手',
            
            # 组合特征
            '短中期组合', '技术成交量组合'
        ]
        
        # 1. 单特征分析
        print("  📊 单特征分析...")
        for feature in analysis_features:
            if feature in df.columns:
                unique_values = sorted([v for v in df[feature].unique() if pd.notna(v)])
                
                for value in unique_values:
                    subset = df[df[feature] == value]
                    if len(subset) >= min_trades:
                        win_rate = subset['是否盈利'].mean()
                        avg_return = subset['收益率'].mean()
                        total_trades = len(subset)
                        
                        if win_rate >= min_win_rate:
                            all_results.append({
                                'type': 'single',
                                'description': f'{feature}={value}',
                                'win_rate': win_rate,
                                'avg_return': avg_return,
                                'total_trades': total_trades,
                                'score': win_rate * avg_return
                            })
        
        # 2. 双特征组合分析
        print("  🔍 双特征组合分析...")
        feature_pairs = [
            ('技术强度', '成交量是前一日几倍'),
            ('技术强度_三分', '成交量_三分'),
            ('技术强度_高强', '成交量_极高'),
            ('技术强度_极高', '成交量_超高'),
            ('技术强度_三分', '连续技术强度3天数_三分'),
            ('技术强度_三分', '连续技术强度5天数_三分'),
            ('连续技术强度3天数_三分', '连续技术强度5天数_三分'),
            ('技术强度_高强', '高波动'),
            ('成交量_极高', '强势收盘'),
            ('技术强度_极低', '成交量_极低'),
        ]
        
        for feature1, feature2 in feature_pairs:
            if feature1 in df.columns and feature2 in df.columns:
                values1 = sorted([v for v in df[feature1].unique() if pd.notna(v)])[:8]
                values2 = sorted([v for v in df[feature2].unique() if pd.notna(v)])[:8]
                
                for val1 in values1:
                    for val2 in values2:
                        subset = df[(df[feature1] == val1) & (df[feature2] == val2)]
                        if len(subset) >= min_trades:
                            win_rate = subset['是否盈利'].mean()
                            avg_return = subset['收益率'].mean()
                            total_trades = len(subset)
                            
                            if win_rate >= min_win_rate:
                                all_results.append({
                                    'type': 'double',
                                    'description': f'{feature1}={val1}, {feature2}={val2}',
                                    'win_rate': win_rate,
                                    'avg_return': avg_return,
                                    'total_trades': total_trades,
                                    'score': win_rate * avg_return
                                })
        
        # 3. 三特征组合分析 (精选)
        print("  🎯 三特征组合分析...")
        triple_combinations = [
            ('技术强度_三分', '连续技术强度3天数_三分', '成交量_三分'),
            ('技术强度_高强', '成交量_极高', '高波动'),
            ('技术强度_极高', '成交量_超高', '强势收盘'),
        ]
        
        for feature1, feature2, feature3 in triple_combinations:
            if all(f in df.columns for f in [feature1, feature2, feature3]):
                values1 = sorted([v for v in df[feature1].unique() if pd.notna(v)])[:5]
                values2 = sorted([v for v in df[feature2].unique() if pd.notna(v)])[:5]
                values3 = sorted([v for v in df[feature3].unique() if pd.notna(v)])[:5]
                
                for val1 in values1:
                    for val2 in values2:
                        for val3 in values3:
                            subset = df[(df[feature1] == val1) & 
                                      (df[feature2] == val2) & 
                                      (df[feature3] == val3)]
                            if len(subset) >= min_trades:
                                win_rate = subset['是否盈利'].mean()
                                avg_return = subset['收益率'].mean()
                                total_trades = len(subset)
                                
                                if win_rate >= min_win_rate:
                                    all_results.append({
                                        'type': 'triple',
                                        'description': f'{feature1}={val1}, {feature2}={val2}, {feature3}={val3}',
                                        'win_rate': win_rate,
                                        'avg_return': avg_return,
                                        'total_trades': total_trades,
                                        'score': win_rate * avg_return
                                    })
        
        # 按评分排序
        all_results.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"  发现 {len(all_results)} 个高胜率组合")
        
        # 显示前50个最佳结果
        if all_results:
            print(f"\n🏆 前50个最佳策略组合:")
            for i, result in enumerate(all_results[:50], 1):
                print(f"  {i:2d}. {result['description']}: "
                      f"胜率{result['win_rate']:.3f}({result['win_rate']*100:.1f}%) "
                      f"收益{result['avg_return']:.4f}({result['avg_return']*100:.2f}%) "
                      f"交易{result['total_trades']}笔 ({result['type']})")
        
        return all_results
    
    def save_results(self, results, output_path):
        """保存结果"""
        if not results:
            print("  ⚠️ 没有结果可保存")
            return
        
        results_data = []
        for result in results:
            results_data.append({
                '组合类型': result['type'],
                '组合描述': result['description'],
                '胜率': f"{result['win_rate']*100:.1f}%",
                '平均收益率': f"{result['avg_return']*100:.2f}%",
                '总交易次数': result['total_trades'],
                '综合评分': f"{result['score']:.6f}"
            })
        
        results_df = pd.DataFrame(results_data)
        results_df.to_excel(output_path, index=False)
        print(f"💾 保存了 {len(results)} 个策略到: {output_path}")

def main():
    """主函数"""
    start_time = datetime.now()
    
    try:
        # 1. 初始化挖掘系统
        mining_system = EfficientComprehensiveMining(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily"
        )
        
        # 2. 加载和处理数据
        trading_df = mining_system.load_and_process_data()
        
        if len(trading_df) == 0:
            print("❌ 没有有效的交易数据")
            return
        
        # 3. 创建增强特征
        trading_df = mining_system.create_enhanced_features(trading_df)
        
        # 4. 全面分析
        all_results = mining_system.comprehensive_analysis(trading_df)
        
        # 5. 保存结果
        output_path = f"高效全面策略挖掘_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        mining_system.save_results(all_results, output_path)
        
        end_time = datetime.now()
        
        print(f"\n" + "="*80)
        print(f"🎉 高效全面策略挖掘完成!")
        print(f"⏱️ 总耗时: {end_time - start_time}")
        print(f"📊 分析了 {len(trading_df):,} 笔实际交易")
        print(f"🎯 发现 {len(all_results)} 个高胜率策略")
        print(f"💰 买卖规则: 当日开盘买入，次日开盘卖出")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
