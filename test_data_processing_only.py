#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据处理部分 - 不依赖TensorFlow
验证增强的特征工程是否正常工作

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入数据处理模块
from integrated_data_processor import IntegratedStockDataProcessor

def test_basic_data_loading():
    """测试基础数据加载"""
    print("=" * 60)
    print("📂 测试基础数据加载")
    print("=" * 60)
    
    try:
        # 创建数据处理器
        data_processor = IntegratedStockDataProcessor(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=20
        )
        
        # 加载股票数据
        print("1. 加载股票基础数据...")
        stock_df = data_processor.load_stock_data()
        print(f"   ✅ 股票数据: {len(stock_df)} 条记录")
        print(f"   📊 股票数据列: {list(stock_df.columns)}")
        print(f"   📅 日期范围: {stock_df['日期'].min()} 到 {stock_df['日期'].max()}")
        
        # 加载技术强度数据
        print("\n2. 加载技术强度数据...")
        tech_df = data_processor.load_tech_strength_data()
        print(f"   ✅ 技术强度数据: {len(tech_df)} 条记录")
        print(f"   📊 技术强度数据列: {list(tech_df.columns)}")
        print(f"   📅 日期范围: {tech_df['日期'].min()} 到 {tech_df['日期'].max()}")
        
        # 显示技术强度分布
        print(f"\n   🎯 技术强度分布:")
        tech_strength_counts = tech_df['技术强度'].value_counts().sort_index()
        for strength, count in tech_strength_counts.items():
            print(f"      技术强度 {strength}: {count} 条记录")
        
        return True, data_processor, stock_df, tech_df
        
    except Exception as e:
        print(f"❌ 基础数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None

def test_data_merging(data_processor, stock_df, tech_df):
    """测试数据合并"""
    print("\n" + "=" * 60)
    print("🔗 测试数据合并")
    print("=" * 60)
    
    try:
        # 合并数据
        print("1. 合并股票数据和技术强度数据...")
        merged_df = data_processor.merge_datasets(stock_df, tech_df)
        print(f"   ✅ 合并后数据: {len(merged_df)} 条记录")
        
        # 检查合并结果
        print(f"   📊 合并后列数: {len(merged_df.columns)}")
        print(f"   📅 日期范围: {merged_df['日期'].min()} 到 {merged_df['日期'].max()}")
        
        # 检查股票数量
        unique_stocks = merged_df['股票代码'].nunique()
        print(f"   🏢 唯一股票数量: {unique_stocks}")
        
        # 显示前几条记录
        print(f"\n   📋 前3条记录:")
        print(merged_df[['股票代码', '日期', '收盘价_stock', '技术强度']].head(3))
        
        return True, merged_df
        
    except Exception as e:
        print(f"❌ 数据合并失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_feature_engineering(data_processor, merged_df):
    """测试特征工程"""
    print("\n" + "=" * 60)
    print("⚙️ 测试特征工程")
    print("=" * 60)
    
    try:
        # 创建价格技术指标
        print("1. 创建价格技术指标...")
        merged_df = data_processor.create_price_features(merged_df)
        print(f"   ✅ 价格特征创建完成")
        
        # 处理技术强度特征
        print("2. 处理技术强度特征...")
        merged_df = data_processor.process_tech_strength_features(merged_df)
        print(f"   ✅ 技术强度特征处理完成")
        
        # 创建高级组合特征
        print("3. 创建高级组合特征...")
        merged_df = data_processor.create_advanced_combination_features(merged_df)
        print(f"   ✅ 高级组合特征创建完成")
        
        # 创建统计学特征
        print("4. 创建统计学特征...")
        merged_df = data_processor.create_statistical_features(merged_df)
        print(f"   ✅ 统计学特征创建完成")
        
        # 创建市场微观结构特征
        print("5. 创建市场微观结构特征...")
        merged_df = data_processor.create_market_microstructure_features(merged_df)
        print(f"   ✅ 市场微观结构特征创建完成")
        
        # 创建行为金融学特征
        print("6. 创建行为金融学特征...")
        merged_df = data_processor.create_behavioral_finance_features(merged_df)
        print(f"   ✅ 行为金融学特征创建完成")
        
        # 准备特征列
        print("7. 准备特征列...")
        merged_df = data_processor.prepare_feature_columns(merged_df)
        print(f"   ✅ 特征列准备完成")
        
        # 显示特征统计
        print(f"\n   📊 特征统计:")
        print(f"      价格特征数量: {len(data_processor.price_feature_columns)}")
        print(f"      技术强度特征数量: {len(data_processor.tech_feature_columns)}")
        print(f"      总特征数量: {len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns)}")
        
        # 清理数据
        original_len = len(merged_df)
        merged_df = merged_df.dropna()
        cleaned_len = len(merged_df)
        print(f"      数据清理: {original_len} -> {cleaned_len} 条记录")
        
        return True, merged_df
        
    except Exception as e:
        print(f"❌ 特征工程失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_feature_analysis(data_processor, merged_df):
    """分析特征质量"""
    print("\n" + "=" * 60)
    print("🔍 特征质量分析")
    print("=" * 60)
    
    try:
        # 分析价格特征
        print("1. 价格特征分析:")
        available_price_features = [col for col in data_processor.price_feature_columns if col in merged_df.columns]
        print(f"   可用价格特征: {len(available_price_features)}/{len(data_processor.price_feature_columns)}")
        
        # 显示前10个价格特征的统计信息
        print("   前10个价格特征统计:")
        for i, feature in enumerate(available_price_features[:10], 1):
            if feature in merged_df.columns:
                stats = merged_df[feature].describe()
                print(f"   {i:2d}. {feature}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}")
        
        # 分析技术强度特征
        print("\n2. 技术强度特征分析:")
        available_tech_features = [col for col in data_processor.tech_feature_columns if col in merged_df.columns]
        print(f"   可用技术强度特征: {len(available_tech_features)}/{len(data_processor.tech_feature_columns)}")
        
        # 显示前10个技术强度特征的统计信息
        print("   前10个技术强度特征统计:")
        for i, feature in enumerate(available_tech_features[:10], 1):
            if feature in merged_df.columns:
                stats = merged_df[feature].describe()
                print(f"   {i:2d}. {feature}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}")
        
        # 分析目标变量
        print("\n3. 目标变量分析:")
        target_col = data_processor.target_column
        if target_col in merged_df.columns:
            target_stats = merged_df[target_col].describe()
            print(f"   目标变量 ({target_col}):")
            print(f"   均值: {target_stats['mean']:.4f}")
            print(f"   标准差: {target_stats['std']:.4f}")
            print(f"   最小值: {target_stats['min']:.4f}")
            print(f"   最大值: {target_stats['max']:.4f}")
            
            # 分析涨跌分布
            positive_ratio = (merged_df[target_col] > 0).mean()
            print(f"   上涨比例: {positive_ratio:.2%}")
            print(f"   下跌比例: {1-positive_ratio:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 测试增强的数据处理系统")
    print("=" * 80)
    
    # 记录开始时间
    start_time = datetime.now()
    
    # 测试1: 基础数据加载
    success1, data_processor, stock_df, tech_df = test_basic_data_loading()
    if not success1:
        print("❌ 基础数据加载失败，停止测试")
        return
    
    # 测试2: 数据合并
    success2, merged_df = test_data_merging(data_processor, stock_df, tech_df)
    if not success2:
        print("❌ 数据合并失败，停止测试")
        return
    
    # 测试3: 特征工程
    success3, final_df = test_feature_engineering(data_processor, merged_df)
    if not success3:
        print("❌ 特征工程失败，停止测试")
        return
    
    # 测试4: 特征分析
    success4 = test_feature_analysis(data_processor, final_df)
    if not success4:
        print("❌ 特征分析失败")
        return
    
    # 计算总耗时
    end_time = datetime.now()
    total_time = end_time - start_time
    
    print("\n" + "=" * 80)
    print("🎉 数据处理测试完成!")
    print("=" * 80)
    print(f"总耗时: {total_time}")
    print(f"基础数据加载: ✅")
    print(f"数据合并: ✅")
    print(f"特征工程: ✅")
    print(f"特征分析: ✅")
    
    print(f"\n📊 最终数据摘要:")
    print(f"   最终数据量: {len(final_df)} 条记录")
    print(f"   股票数量: {final_df['股票代码'].nunique()}")
    print(f"   总特征数: {len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns)}")
    print(f"   数据完整性: {(1 - final_df.isnull().sum().sum() / (len(final_df) * len(final_df.columns))):.2%}")

if __name__ == "__main__":
    main()
