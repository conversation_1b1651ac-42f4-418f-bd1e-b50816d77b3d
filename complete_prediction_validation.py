#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整的预测-验证系统
1. 对预测文件资料中的数据进行预测
2. 与实际结果进行统计分析

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
from sklearn.preprocessing import LabelEncoder

class CompletePredictionValidation:
    """完整的预测-验证系统"""
    
    def __init__(self, prediction_dir="预测文件资料", stock_data_dir="stock_data/daily"):
        self.prediction_dir = prediction_dir
        self.stock_data_dir = stock_data_dir
        self.label_encoders = {}
        
        # 高胜率预测规则 (使用明确组合名称)
        self.prediction_rules = [
            # 超高胜率组合 (≥95%)
            {
                'name': '[完美信号] 连续强势+趋势确认+高涨跌幅组合',
                'condition': lambda row: (row.get('连续技术强度5天数_三分', 0) == 3.0 and
                                        row.get('趋势组合_编码', 0) == 2 and
                                        row.get('涨跌幅_分段', 0) == 5.0),
                'probability': 0.988,
                'expected_return': 0.1811,
                'confidence': 'PERFECT'
            },
            
            # 优秀胜率组合 (85-95%)
            {
                'name': '[黄金信号] 技术强度满分组合',
                'condition': lambda row: row.get('技术强度', 0) == 100,
                'probability': 0.890,
                'expected_return': 0.0343,
                'confidence': 'EXCELLENT'
            },
            {
                'name': '[钻石组合] 满分技术+超高成交量组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 3.5),
                'probability': 0.891,
                'expected_return': 0.0661,
                'confidence': 'EXCELLENT'
            },
            {
                'name': '[白金组合] 满分技术+高成交量组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 3.0),
                'probability': 0.912,
                'expected_return': 0.0543,
                'confidence': 'EXCELLENT'
            },
            {
                'name': '[银质组合] 满分技术+中高成交量组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 2.5),
                'probability': 0.924,
                'expected_return': 0.0504,
                'confidence': 'EXCELLENT'
            },
            
            # 很好胜率组合 (80-85%)
            {
                'name': '[稳健信号] 高技术强度组合',
                'condition': lambda row: row.get('技术强度', 0) == 85,
                'probability': 0.813,
                'expected_return': 0.0203,
                'confidence': 'VERY_GOOD'
            },
            {
                'name': '[热点组合] 高技术+超高成交量组合',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 3.5),
                'probability': 0.849,
                'expected_return': 0.0463,
                'confidence': 'VERY_GOOD'
            },
            
            # 良好胜率组合 (75-80%)
            {
                'name': '[放量信号] 超高成交量组合',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 3.5,
                'probability': 0.786,
                'expected_return': 0.0402,
                'confidence': 'GOOD'
            },
            {
                'name': '[活跃信号] 高成交量组合',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 3.0,
                'probability': 0.808,
                'expected_return': 0.0340,
                'confidence': 'GOOD'
            },
            
            # 中等胜率组合 (70-75%)
            {
                'name': '[基础信号] 中等技术强度组合',
                'condition': lambda row: row.get('技术强度', 0) == 71,
                'probability': 0.744,
                'expected_return': 0.0074,
                'confidence': 'MODERATE'
            }
        ]
    
    def create_features(self, df):
        """创建预测特征"""
        # 处理分类特征
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for feature in categorical_features:
            if feature in df.columns:
                df[feature] = df[feature].fillna('Unknown')
                if feature not in self.label_encoders:
                    self.label_encoders[feature] = LabelEncoder()
                    df[f'{feature}_编码'] = self.label_encoders[feature].fit_transform(df[feature].astype(str))
        
        # 连续技术强度分段
        if '连续技术强度5天数' in df.columns:
            min_val, max_val = 28, 500
            range_size = max_val - min_val
            cut1 = min_val + range_size / 3
            cut2 = min_val + 2 * range_size / 3
            df['连续技术强度5天数_三分'] = pd.cut(
                df['连续技术强度5天数'], 
                bins=[min_val-1, cut1, cut2, max_val+1], 
                labels=[1, 2, 3]
            ).astype(float)
        
        # 涨跌幅分段
        if '涨跌幅' in df.columns:
            df['涨跌幅_分段'] = pd.cut(df['涨跌幅'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        return df

    def _get_signal_level(self, rule_name):
        """获取信号等级"""
        if '[完美信号]' in rule_name:
            return '🎯 完美信号'
        elif '[黄金信号]' in rule_name:
            return '🥇 黄金信号'
        elif '[钻石组合]' in rule_name or '[白金组合]' in rule_name or '[银质组合]' in rule_name:
            return '💎 钻石级组合'
        elif '[稳健信号]' in rule_name:
            return '🥈 稳健信号'
        elif '[热点组合]' in rule_name or '[活跃组合]' in rule_name or '[强势组合]' in rule_name:
            return '🔥 热点信号'
        elif '[放量信号]' in rule_name or '[活跃信号]' in rule_name:
            return '📊 放量信号'
        elif '[基础信号]' in rule_name:
            return '🥉 基础信号'
        else:
            return '➡️ 普通信号'

    def predict_stock(self, row):
        """预测单只股票"""
        # 按优先级检查规则 (从高胜率到低胜率)
        for rule in self.prediction_rules:
            try:
                if rule['condition'](row):
                    return {
                        'rule_name': rule['name'],
                        'predicted_probability': rule['probability'],
                        'predicted_return': rule['expected_return'],
                        'confidence': rule['confidence'],
                        'predicted_up': rule['probability'] > 0.5
                    }
            except:
                continue
        
        # 默认预测 (无匹配规则)
        return {
            'rule_name': '[无匹配] 市场平均水平',
            'predicted_probability': 0.536,
            'predicted_return': 0.0022,
            'confidence': 'NEUTRAL',
            'predicted_up': True
        }
    
    def process_prediction_file(self, date_str):
        """处理单个预测文件"""
        print("=" * 80)
        print(f"🔮 预测-验证分析: {date_str}")
        print("=" * 80)
        
        # 1. 加载预测数据
        filename = f"tech_strength_strong_{date_str}_smart.xlsx"
        file_path = os.path.join(self.prediction_dir, filename)
        
        if not os.path.exists(file_path):
            print(f"❌ 预测文件不存在: {filename}")
            return None
        
        try:
            df = pd.read_excel(file_path)
            print(f"📂 加载预测数据: {filename} ({len(df)} 只股票)")
        except Exception as e:
            print(f"❌ 加载预测文件失败: {e}")
            return None
        
        # 2. 创建特征
        df = self.create_features(df)
        
        # 3. 生成预测
        print(f"🎯 生成预测...")
        predictions = []
        for _, row in df.iterrows():
            pred = self.predict_stock(row)
            predictions.append(pred)
        
        # 添加预测结果
        df['预测规则'] = [p['rule_name'] for p in predictions]
        df['信号等级'] = [self._get_signal_level(p['rule_name']) for p in predictions]
        df['预测概率'] = [p['predicted_probability'] for p in predictions]
        df['预测收益率'] = [p['predicted_return'] for p in predictions]
        df['预测上涨'] = [p['predicted_up'] for p in predictions]
        df['置信等级'] = [p['confidence'] for p in predictions]
        
        # 4. 统计预测分布
        print(f"\n📊 预测分布统计:")

        # 按信号等级统计
        print(f"\n  🎯 按信号等级分布:")
        signal_counts = df['信号等级'].value_counts()
        total = len(df)

        for signal_level, count in signal_counts.items():
            percentage = count / total * 100
            avg_prob = df[df['信号等级'] == signal_level]['预测概率'].mean()
            print(f"    {signal_level}: {count} 只 ({percentage:.1f}%) - 平均概率{avg_prob:.1%}")

        # 按具体规则统计
        print(f"\n  📋 按具体规则分布:")
        rule_counts = df['预测规则'].value_counts()

        for rule_name, count in rule_counts.head(10).items():
            percentage = count / total * 100
            avg_prob = df[df['预测规则'] == rule_name]['预测概率'].mean()
            signal_level = df[df['预测规则'] == rule_name]['信号等级'].iloc[0]
            print(f"    {signal_level} - {rule_name}: {count} 只 ({percentage:.1f}%)")
        
        # 5. 加载实际数据
        prediction_date = datetime.strptime(date_str, '%Y-%m-%d')
        next_date = prediction_date + timedelta(days=1)
        
        # 跳过周末
        while next_date.weekday() >= 5:
            next_date += timedelta(days=1)
        
        actual_data = self.load_actual_data(prediction_date, next_date)
        if actual_data is None:
            print(f"❌ 无法加载实际数据")
            return None
        
        # 6. 计算实际收益
        actual_returns = self.calculate_actual_returns(actual_data, prediction_date, next_date)
        if len(actual_returns) == 0:
            print(f"❌ 无法计算实际收益")
            return None
        
        # 7. 合并预测和实际结果
        merged_df = pd.merge(df, actual_returns, on='股票代码', how='inner')
        
        if len(merged_df) == 0:
            print(f"❌ 预测数据与实际数据无法匹配")
            return None
        
        print(f"🔗 成功匹配: {len(merged_df)} 只股票")
        
        return merged_df, prediction_date, next_date
    
    def load_actual_data(self, prediction_date, next_date):
        """加载实际交易数据"""
        prediction_file = f"stock_data_{prediction_date.strftime('%Y%m%d')}.xlsx"
        next_file = f"stock_data_{next_date.strftime('%Y%m%d')}.xlsx"
        
        prediction_path = os.path.join(self.stock_data_dir, prediction_file)
        next_path = os.path.join(self.stock_data_dir, next_file)
        
        actual_data = []
        
        # 加载预测日数据
        if os.path.exists(prediction_path):
            try:
                df_pred = pd.read_excel(prediction_path)
                df_pred['日期'] = prediction_date
                actual_data.append(df_pred)
                print(f"📊 加载预测日数据: {prediction_file}")
            except Exception as e:
                print(f"❌ 加载 {prediction_file} 失败: {e}")
        
        # 加载次日数据
        if os.path.exists(next_path):
            try:
                df_next = pd.read_excel(next_path)
                df_next['日期'] = next_date
                actual_data.append(df_next)
                print(f"📊 加载次日数据: {next_file}")
            except Exception as e:
                print(f"❌ 加载 {next_file} 失败: {e}")
        
        if actual_data:
            combined_df = pd.concat(actual_data, ignore_index=True)
            return combined_df
        
        return None
    
    def calculate_actual_returns(self, actual_data, prediction_date, next_date):
        """计算实际收益率"""
        print(f"💹 计算实际收益率...")
        actual_returns = []
        
        for stock_code, group in actual_data.groupby('证券代码'):
            group = group.sort_values('日期')
            
            # 查找预测日和次日数据
            pred_day = group[group['日期'] == prediction_date]
            next_day = group[group['日期'] == next_date]
            
            if len(pred_day) > 0 and len(next_day) > 0:
                buy_price = pred_day.iloc[0]['开盘价']  # 预测日开盘价买入
                sell_price = next_day.iloc[0]['开盘价']  # 次日开盘价卖出
                
                if buy_price > 0 and sell_price > 0:
                    actual_return = (sell_price - buy_price) / buy_price
                    
                    actual_returns.append({
                        '股票代码': stock_code,
                        '买入价格': buy_price,
                        '卖出价格': sell_price,
                        '实际收益率': actual_return,
                        '实际上涨': actual_return > 0
                    })
        
        print(f"  计算了 {len(actual_returns)} 只股票的实际收益")
        return pd.DataFrame(actual_returns)
    
    def analyze_prediction_accuracy(self, merged_df, prediction_date, next_date):
        """分析预测准确性"""
        print(f"\n📊 预测准确性分析:")
        print(f"  预测日期: {prediction_date.strftime('%Y-%m-%d')}")
        print(f"  验证日期: {next_date.strftime('%Y-%m-%d')}")
        
        total_stocks = len(merged_df)
        
        # 1. 整体准确性
        correct_predictions = (merged_df['预测上涨'] == merged_df['实际上涨']).sum()
        overall_accuracy = correct_predictions / total_stocks
        
        print(f"\n  📈 整体预测表现:")
        print(f"    总股票数: {total_stocks:,}")
        print(f"    正确预测: {correct_predictions:,}")
        print(f"    整体准确率: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)")
        
        # 2. 按预测规则分析
        print(f"\n  🎯 各预测规则表现:")
        rule_analysis = []
        
        for rule_name in merged_df['预测规则'].unique():
            subset = merged_df[merged_df['预测规则'] == rule_name]
            if len(subset) > 0:
                rule_accuracy = (subset['预测上涨'] == subset['实际上涨']).mean()
                actual_win_rate = subset['实际上涨'].mean()
                predicted_prob = subset['预测概率'].mean()
                actual_return = subset['实际收益率'].mean()
                predicted_return = subset['预测收益率'].mean()
                count = len(subset)
                
                prob_bias = predicted_prob - actual_win_rate
                return_bias = predicted_return - actual_return
                
                # 评估表现
                if actual_win_rate >= predicted_prob * 0.95:
                    performance = "✅ 优秀"
                elif actual_win_rate >= predicted_prob * 0.85:
                    performance = "👍 良好"
                elif actual_win_rate >= predicted_prob * 0.75:
                    performance = "⚠️ 一般"
                else:
                    performance = "❌ 较差"
                
                print(f"\n    {rule_name}: {count}只")
                print(f"      预测准确率: {rule_accuracy:.3f} ({rule_accuracy*100:.1f}%)")
                print(f"      预测概率: {predicted_prob:.3f} vs 实际胜率: {actual_win_rate:.3f}")
                print(f"      预测收益: {predicted_return:.4f} vs 实际收益: {actual_return:.4f}")
                print(f"      概率偏差: {prob_bias:+.3f} ({prob_bias*100:+.1f}%)")
                print(f"      收益偏差: {return_bias:+.4f} ({return_bias*100:+.2f}%)")
                print(f"      表现评级: {performance}")
                
                rule_analysis.append({
                    'rule_name': rule_name,
                    'count': count,
                    'accuracy': rule_accuracy,
                    'predicted_prob': predicted_prob,
                    'actual_win_rate': actual_win_rate,
                    'predicted_return': predicted_return,
                    'actual_return': actual_return,
                    'prob_bias': prob_bias,
                    'return_bias': return_bias,
                    'performance': performance
                })
        
        # 3. 按信号等级分析
        print(f"\n  🎯 按信号等级分析:")
        signal_analysis = []

        for signal_level in merged_df['信号等级'].unique():
            subset = merged_df[merged_df['信号等级'] == signal_level]
            if len(subset) > 0:
                signal_accuracy = (subset['预测上涨'] == subset['实际上涨']).mean()
                actual_win_rate = subset['实际上涨'].mean()
                predicted_prob = subset['预测概率'].mean()
                actual_return = subset['实际收益率'].mean()
                count = len(subset)

                print(f"\n    {signal_level}: {count}只")
                print(f"      预测准确率: {signal_accuracy:.3f} ({signal_accuracy*100:.1f}%)")
                print(f"      预测概率: {predicted_prob:.3f} vs 实际胜率: {actual_win_rate:.3f}")
                print(f"      实际平均收益: {actual_return:.4f} ({actual_return*100:.2f}%)")

                signal_analysis.append({
                    'signal_level': signal_level,
                    'count': count,
                    'accuracy': signal_accuracy,
                    'actual_win_rate': actual_win_rate,
                    'actual_return': actual_return
                })

        # 4. 高置信度预测分析
        print(f"\n  🏆 高置信度预测分析 (概率≥85%):")
        high_conf = merged_df[merged_df['预测概率'] >= 0.85]
        if len(high_conf) > 0:
            high_conf_accuracy = (high_conf['预测上涨'] == high_conf['实际上涨']).mean()
            high_conf_win_rate = high_conf['实际上涨'].mean()
            high_conf_return = high_conf['实际收益率'].mean()

            print(f"    高置信度股票: {len(high_conf)}只")
            print(f"    预测准确率: {high_conf_accuracy:.3f} ({high_conf_accuracy*100:.1f}%)")
            print(f"    实际胜率: {high_conf_win_rate:.3f} ({high_conf_win_rate*100:.1f}%)")
            print(f"    实际平均收益: {high_conf_return:.4f} ({high_conf_return*100:.2f}%)")
            
            # 显示具体的高置信度预测结果
            print(f"\n    具体结果 (前10只):")
            high_conf_sorted = high_conf.nlargest(10, '预测概率')
            
            for i, (_, row) in enumerate(high_conf_sorted.iterrows(), 1):
                stock_code = row['股票代码']
                stock_name = row.get('股票名称', '')
                predicted_prob = row['预测概率']
                actual_return = row['实际收益率']
                is_correct = "✅" if row['预测上涨'] == row['实际上涨'] else "❌"
                rule_name = row['预测规则']
                
                print(f"      {i:2d}. {is_correct} {stock_code} {stock_name}")
                print(f"          {rule_name}")
                print(f"          预测: {predicted_prob:.1%} → 实际: {actual_return:+.2%}")
        
        return rule_analysis, overall_accuracy
    
    def save_complete_results(self, merged_df, rule_analysis, overall_accuracy, prediction_date, output_path):
        """保存完整的预测验证结果"""
        # 准备详细结果
        detailed_results = merged_df[[
            '股票代码', '股票名称',
            '信号等级', '预测规则', '预测概率', '预测收益率', '预测上涨', '置信等级',
            '实际收益率', '实际上涨',
            '买入价格', '卖出价格',
            '技术强度', '成交量是前一日几倍', '日内股票标记'
        ]].copy()
        
        # 添加分析列
        detailed_results['预测正确'] = detailed_results['预测上涨'] == detailed_results['实际上涨']
        detailed_results['概率偏差'] = detailed_results['预测概率'] - detailed_results['实际上涨'].astype(float)
        detailed_results['收益偏差'] = detailed_results['预测收益率'] - detailed_results['实际收益率']
        
        # 按预测概率排序
        detailed_results = detailed_results.sort_values('预测概率', ascending=False)
        
        # 保存到Excel
        with pd.ExcelWriter(output_path) as writer:
            # 详细预测验证结果
            detailed_results.to_excel(writer, sheet_name='详细预测验证', index=False)
            
            # 规则表现摘要
            if rule_analysis:
                summary_data = []
                for rule in rule_analysis:
                    summary_data.append({
                        '预测规则': rule['rule_name'],
                        '股票数量': rule['count'],
                        '预测准确率': f"{rule['accuracy']*100:.1f}%",
                        '预测概率': f"{rule['predicted_prob']*100:.1f}%",
                        '实际胜率': f"{rule['actual_win_rate']*100:.1f}%",
                        '预测收益率': f"{rule['predicted_return']*100:.2f}%",
                        '实际收益率': f"{rule['actual_return']*100:.2f}%",
                        '概率偏差': f"{rule['prob_bias']*100:+.1f}%",
                        '收益偏差': f"{rule['return_bias']*100:+.2f}%",
                        '表现评级': rule['performance']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='规则表现摘要', index=False)
        
        print(f"💾 完整预测验证结果已保存到: {output_path}")

def main():
    """主函数"""
    validator = CompletePredictionValidation()
    
    # 要分析的日期
    analysis_dates = ['2025-06-30', '2025-07-01', '2025-07-02', '2025-07-03']
    
    all_results = []
    
    for date_str in analysis_dates:
        try:
            print(f"\n" + "="*80)
            
            # 处理预测文件
            result = validator.process_prediction_file(date_str)
            
            if result is not None:
                merged_df, prediction_date, next_date = result
                
                # 分析预测准确性
                rule_analysis, overall_accuracy = validator.analyze_prediction_accuracy(merged_df, prediction_date, next_date)
                
                # 保存结果
                output_path = f"完整预测验证_{date_str.replace('-', '')}.xlsx"
                validator.save_complete_results(merged_df, rule_analysis, overall_accuracy, prediction_date, output_path)
                
                all_results.append({
                    'date': date_str,
                    'accuracy': overall_accuracy,
                    'total_stocks': len(merged_df),
                    'rule_analysis': rule_analysis
                })
                
                print(f"✅ {date_str} 完整预测验证完成")
            else:
                print(f"❌ {date_str} 验证失败")
                
        except Exception as e:
            print(f"❌ 验证 {date_str} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 总结所有结果
    if all_results:
        print(f"\n" + "="*80)
        print(f"📊 完整预测验证总结")
        print(f"="*80)
        
        total_accuracy = sum(r['accuracy'] for r in all_results) / len(all_results)
        total_stocks = sum(r['total_stocks'] for r in all_results)
        
        print(f"验证日期数: {len(all_results)}")
        print(f"总验证股票数: {total_stocks:,}")
        print(f"平均预测准确率: {total_accuracy:.3f} ({total_accuracy*100:.1f}%)")
        
        print(f"\n各日期准确率:")
        for result in all_results:
            print(f"  {result['date']}: {result['accuracy']:.3f} ({result['accuracy']*100:.1f}%) - {result['total_stocks']}只股票")
        
        # 分析最佳表现的规则
        print(f"\n🏆 最佳表现规则统计:")
        all_rules = {}
        for result in all_results:
            for rule in result['rule_analysis']:
                rule_name = rule['rule_name']
                if rule_name not in all_rules:
                    all_rules[rule_name] = []
                all_rules[rule_name].append(rule['actual_win_rate'])
        
        for rule_name, win_rates in all_rules.items():
            avg_win_rate = sum(win_rates) / len(win_rates)
            if avg_win_rate >= 0.80:  # 只显示平均胜率≥80%的规则
                print(f"  {rule_name}: 平均胜率{avg_win_rate:.1%} ({len(win_rates)}个交易日)")
    
    print(f"\n🎉 完整预测验证系统运行完成！")

if __name__ == "__main__":
    main()
