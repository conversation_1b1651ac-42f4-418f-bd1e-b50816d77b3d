#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级多特征组合策略挖掘
使用3-8个特征的复杂组合进行更精准的分类和预测

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')
from itertools import combinations, product
from sklearn.preprocessing import LabelEncoder

class AdvancedMultiFeatureStrategy:
    """高级多特征组合策略挖掘系统"""
    
    def __init__(self, tech_strength_path, stock_data_path):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.label_encoders = {}
        self.all_strategies = []
    
    def load_and_process_data(self):
        """加载并处理数据"""
        print("=" * 80)
        print("🔍 高级多特征组合策略挖掘系统")
        print("📋 特征组合: 3-8个特征的复杂组合分析")
        print("📋 买卖规则: 当日开盘买入，次日开盘卖出")
        print("=" * 80)
        
        # 加载数据
        print("📂 加载数据...")
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"  技术强度数据: {len(tech_df)} 条")
        print(f"  股票交易数据: {len(stock_df)} 条")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 合并数据
        print("🔗 合并数据...")
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='inner',
            suffixes=('_tech', '_stock')
        )
        
        print(f"  合并后数据: {len(merged_df)} 条")
        
        # 计算交易收益
        print("💹 计算交易收益...")
        trading_results = []
        
        for stock_code, group in merged_df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            for i in range(len(group) - 1):
                current_row = group.iloc[i]
                next_row = group.iloc[i + 1]
                
                buy_price = current_row['开盘价']
                sell_price = next_row['开盘价']
                
                if buy_price > 0 and sell_price > 0:
                    return_rate = (sell_price - buy_price) / buy_price
                    
                    trading_record = {
                        '股票代码': stock_code,
                        '收益率': return_rate,
                        '盈利金额': sell_price - buy_price,
                        '是否盈利': return_rate > 0,
                        
                        # 所有可用特征
                        '技术强度': current_row['技术强度'],
                        '技术指标特征': current_row.get('技术指标特征', ''),
                        '趋势组合': current_row.get('趋势组合', ''),
                        '成交量是前一日几倍': current_row.get('成交量是前一日几倍', 0),
                        '日内股票标记': current_row.get('日内股票标记', ''),
                        '连续技术强度3天数': current_row.get('连续技术强度3天数', 0),
                        '连续技术强度5天数': current_row.get('连续技术强度5天数', 0),
                        '连续技术强度10天数': current_row.get('连续技术强度10天数', 0),
                        
                        # 价格特征
                        '开盘价': current_row['开盘价'],
                        '最高价': current_row['最高价'],
                        '最低价': current_row['最低价'],
                        '收盘价': current_row.get('收盘价_stock', current_row.get('收盘价', 0)),
                        '前收盘价': current_row.get('前收盘价', 0),
                        '换手率': current_row.get('换手率', 0),
                        '涨跌幅': current_row.get('涨跌幅_stock', current_row.get('涨跌幅', 0)),
                        '成交量': current_row.get('成交量_stock', current_row.get('成交量', 0)),
                        '成交额': current_row.get('成交额', 0),
                    }
                    
                    trading_results.append(trading_record)
        
        trading_df = pd.DataFrame(trading_results)
        print(f"  生成交易记录: {len(trading_df)} 笔")
        
        if len(trading_df) > 0:
            win_rate = trading_df['是否盈利'].mean()
            avg_return = trading_df['收益率'].mean()
            print(f"  整体胜率: {win_rate:.3f} ({win_rate*100:.1f}%)")
            print(f"  平均收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")
        
        return trading_df
    
    def create_advanced_features(self, df):
        """创建高级特征"""
        print(f"\n🔧 创建高级多维特征...")
        
        # 1. 处理分类特征
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for feature in categorical_features:
            if feature in df.columns:
                df[feature] = df[feature].fillna('Unknown')
                if feature not in self.label_encoders:
                    self.label_encoders[feature] = LabelEncoder()
                    df[f'{feature}_编码'] = self.label_encoders[feature].fit_transform(df[feature].astype(str))
        
        # 2. 技术强度多级分段
        if '技术强度' in df.columns:
            df['技术强度_三分'] = pd.cut(df['技术强度'], bins=[27, 42, 71, 101], labels=[1, 2, 3]).astype(float)
            df['技术强度_五分'] = pd.cut(df['技术强度'], bins=[27, 35, 50, 65, 85, 101], labels=[1, 2, 3, 4, 5]).astype(float)
            df['技术强度_极值'] = df['技术强度'].apply(lambda x: 1 if x == 100 else (2 if x >= 85 else (3 if x >= 71 else (4 if x >= 57 else 5))))
        
        # 3. 连续技术强度分段
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500},
            '连续技术强度10天数': {'min': 28, 'max': 956}
        }
        
        for indicator, info in continuous_indicators.items():
            if indicator in df.columns:
                min_val, max_val = info['min'], info['max']
                range_size = max_val - min_val
                
                # 三分段
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                df[f'{indicator}_三分'] = pd.cut(
                    df[indicator], 
                    bins=[min_val-1, cut1, cut2, max_val+1], 
                    labels=[1, 2, 3]
                ).astype(float)
                
                # 五分段
                cuts = [min_val-1] + [min_val + i*range_size/5 for i in range(1, 5)] + [max_val+1]
                df[f'{indicator}_五分'] = pd.cut(
                    df[indicator], bins=cuts, labels=[1, 2, 3, 4, 5]
                ).astype(float)
        
        # 4. 成交量多级分段
        if '成交量是前一日几倍' in df.columns:
            df['成交量_三分'] = pd.cut(
                df['成交量是前一日几倍'], 
                bins=[0, 1.0, 2.0, 4.0], 
                labels=[1, 2, 3]
            ).astype(float)
            
            df['成交量_五分'] = pd.cut(
                df['成交量是前一日几倍'], 
                bins=[0, 0.8, 1.2, 1.8, 2.5, 4.0], 
                labels=[1, 2, 3, 4, 5]
            ).astype(float)
            
            df['成交量_七分'] = pd.cut(
                df['成交量是前一日几倍'], 
                bins=[0, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0], 
                labels=[1, 2, 3, 4, 5, 6, 7]
            ).astype(float)
        
        # 5. 价格衍生特征
        if all(col in df.columns for col in ['开盘价', '最高价', '最低价', '收盘价']):
            # 价格波动率
            df['价格波动率'] = (df['最高价'] - df['最低价']) / df['收盘价']
            df['价格波动率_分段'] = pd.cut(df['价格波动率'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
            
            # 开盘缺口
            if '前收盘价' in df.columns:
                df['开盘缺口'] = (df['开盘价'] - df['前收盘价']) / df['前收盘价']
                df['开盘缺口_分段'] = pd.cut(df['开盘缺口'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
            
            # 收盘强度
            price_range = df['最高价'] - df['最低价']
            price_range = price_range.replace(0, 1e-8)
            df['收盘强度'] = (df['收盘价'] - df['最低价']) / price_range
            df['收盘强度_分段'] = pd.cut(df['收盘强度'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 6. 换手率和涨跌幅分段
        if '换手率' in df.columns:
            df['换手率_分段'] = pd.cut(df['换手率'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        if '涨跌幅' in df.columns:
            df['涨跌幅_分段'] = pd.cut(df['涨跌幅'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 7. 组合特征
        if '连续技术强度3天数_三分' in df.columns and '连续技术强度5天数_三分' in df.columns:
            df['短中期组合'] = df['连续技术强度3天数_三分'] * 10 + df['连续技术强度5天数_三分']
        
        if '连续技术强度5天数_三分' in df.columns and '连续技术强度10天数_三分' in df.columns:
            df['中长期组合'] = df['连续技术强度5天数_三分'] * 10 + df['连续技术强度10天数_三分']
        
        if '技术强度_三分' in df.columns and '成交量_三分' in df.columns:
            df['技术成交量组合'] = df['技术强度_三分'] * 10 + df['成交量_三分']
        
        # 8. 市场状态特征
        if '技术强度' in df.columns and '成交量是前一日几倍' in df.columns:
            df['市场热度'] = df['技术强度'] * df['成交量是前一日几倍']
            df['市场热度_分段'] = pd.cut(df['市场热度'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        print(f"  特征创建完成，当前特征数: {len(df.columns)}")
        return df
    
    def advanced_multi_feature_mining(self, df, min_trades=20, min_win_rate=0.70):
        """高级多特征组合挖掘"""
        print(f"\n🎯 高级多特征组合挖掘 (3-8个特征组合)...")
        print(f"  筛选条件: 最少{min_trades}笔交易, 胜率≥{min_win_rate*100:.0f}%")
        
        all_strategies = []
        
        # 定义特征组
        feature_groups = {
            'core_tech': ['技术强度', '技术强度_三分', '技术强度_五分', '技术强度_极值'],
            'continuous': ['连续技术强度3天数_三分', '连续技术强度5天数_三分', '连续技术强度10天数_三分',
                          '连续技术强度3天数_五分', '连续技术强度5天数_五分', '连续技术强度10天数_五分'],
            'volume': ['成交量是前一日几倍', '成交量_三分', '成交量_五分', '成交量_七分'],
            'price': ['价格波动率_分段', '开盘缺口_分段', '收盘强度_分段'],
            'market': ['换手率_分段', '涨跌幅_分段', '市场热度_分段'],
            'categorical': ['技术指标特征_编码', '趋势组合_编码', '日内股票标记_编码'],
            'combinations': ['短中期组合', '中长期组合', '技术成交量组合']
        }
        
        # 1. 三特征组合分析
        print(f"  🔍 三特征组合分析...")
        three_feature_combinations = [
            ('core_tech', 'volume', 'categorical'),
            ('core_tech', 'continuous', 'volume'),
            ('core_tech', 'volume', 'price'),
            ('core_tech', 'categorical', 'market'),
            ('continuous', 'volume', 'categorical'),
            ('volume', 'price', 'market'),
            ('core_tech', 'continuous', 'categorical'),
            ('continuous', 'categorical', 'market'),
        ]
        
        for group1, group2, group3 in three_feature_combinations:
            features1 = [f for f in feature_groups[group1] if f in df.columns][:3]
            features2 = [f for f in feature_groups[group2] if f in df.columns][:3]
            features3 = [f for f in feature_groups[group3] if f in df.columns][:3]
            
            for f1 in features1:
                for f2 in features2:
                    for f3 in features3:
                        if f1 != f2 and f2 != f3 and f1 != f3:
                            strategies = self._analyze_feature_combination(
                                df, [f1, f2, f3], min_trades, min_win_rate, '三特征'
                            )
                            all_strategies.extend(strategies)
        
        # 2. 四特征组合分析
        print(f"  🔍 四特征组合分析...")
        four_feature_combinations = [
            ('core_tech', 'continuous', 'volume', 'categorical'),
            ('core_tech', 'volume', 'price', 'market'),
            ('core_tech', 'volume', 'categorical', 'combinations'),
            ('continuous', 'volume', 'categorical', 'market'),
        ]
        
        for group1, group2, group3, group4 in four_feature_combinations:
            features1 = [f for f in feature_groups[group1] if f in df.columns][:2]
            features2 = [f for f in feature_groups[group2] if f in df.columns][:2]
            features3 = [f for f in feature_groups[group3] if f in df.columns][:2]
            features4 = [f for f in feature_groups[group4] if f in df.columns][:2]
            
            for f1 in features1:
                for f2 in features2:
                    for f3 in features3:
                        for f4 in features4:
                            if len(set([f1, f2, f3, f4])) == 4:  # 确保特征不重复
                                strategies = self._analyze_feature_combination(
                                    df, [f1, f2, f3, f4], min_trades, min_win_rate, '四特征'
                                )
                                all_strategies.extend(strategies)
        
        # 3. 五特征组合分析 (精选)
        print(f"  🔍 五特征组合分析...")
        five_feature_sets = [
            ['技术强度', '成交量是前一日几倍', '技术指标特征_编码', '价格波动率_分段', '换手率_分段'],
            ['技术强度_三分', '连续技术强度3天数_三分', '成交量_三分', '日内股票标记_编码', '市场热度_分段'],
            ['技术强度_极值', '成交量_五分', '趋势组合_编码', '收盘强度_分段', '涨跌幅_分段'],
        ]
        
        for feature_set in five_feature_sets:
            available_features = [f for f in feature_set if f in df.columns]
            if len(available_features) >= 5:
                strategies = self._analyze_feature_combination(
                    df, available_features[:5], min_trades, min_win_rate, '五特征'
                )
                all_strategies.extend(strategies)
        
        # 按评分排序
        all_strategies.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"  发现 {len(all_strategies)} 个高胜率多特征策略")
        
        return all_strategies
    
    def _analyze_feature_combination(self, df, features, min_trades, min_win_rate, combo_type):
        """分析特征组合"""
        strategies = []
        
        # 获取每个特征的主要值
        feature_values = {}
        for feature in features:
            if feature in df.columns:
                unique_vals = sorted([v for v in df[feature].unique() if pd.notna(v)])
                # 限制值的数量以控制组合爆炸
                if len(unique_vals) > 5:
                    # 对于数值特征，选择极值和中间值
                    if feature.endswith('_编码'):
                        feature_values[feature] = unique_vals[:5]
                    else:
                        feature_values[feature] = [unique_vals[0], unique_vals[len(unique_vals)//4], 
                                                 unique_vals[len(unique_vals)//2], unique_vals[3*len(unique_vals)//4], 
                                                 unique_vals[-1]]
                else:
                    feature_values[feature] = unique_vals
        
        # 生成组合
        if len(feature_values) == len(features):
            # 限制组合数量
            max_combinations = 100
            combination_count = 0
            
            for combination in product(*[feature_values[f] for f in features]):
                if combination_count >= max_combinations:
                    break
                
                # 构建查询条件
                mask = pd.Series([True] * len(df))
                for i, feature in enumerate(features):
                    mask &= (df[feature] == combination[i])
                
                subset = df[mask]
                
                if len(subset) >= min_trades:
                    win_rate = subset['是否盈利'].mean()
                    avg_return = subset['收益率'].mean()
                    avg_profit = subset['盈利金额'].mean()
                    
                    if win_rate >= min_win_rate:
                        # 构建描述
                        description = ', '.join([f'{features[i]}={combination[i]}' for i in range(len(features))])
                        
                        strategies.append({
                            'type': combo_type,
                            'features': dict(zip(features, combination)),
                            'description': description,
                            'win_rate': win_rate,
                            'avg_return': avg_return,
                            'avg_profit': avg_profit,
                            'total_trades': len(subset),
                            'score': win_rate * avg_return,
                            'feature_count': len(features)
                        })
                        
                        combination_count += 1
        
        return strategies
    
    def display_advanced_results(self, strategies, top_n=30):
        """显示高级结果"""
        if not strategies:
            print("  ⚠️ 没有找到符合条件的策略")
            return
        
        print(f"\n🏆 前{min(top_n, len(strategies))}个最佳多特征策略:")
        
        for i, strategy in enumerate(strategies[:top_n], 1):
            print(f"  {i:2d}. [{strategy['type']}] {strategy['description']}")
            print(f"      胜率: {strategy['win_rate']:.3f} ({strategy['win_rate']*100:.1f}%)")
            print(f"      平均收益率: {strategy['avg_return']:.4f} ({strategy['avg_return']*100:.2f}%)")
            print(f"      平均盈利: {strategy['avg_profit']:.2f}元")
            print(f"      交易次数: {strategy['total_trades']}")
            print(f"      综合评分: {strategy['score']:.6f}")
            print()
    
    def save_advanced_results(self, strategies, output_path):
        """保存高级结果"""
        if not strategies:
            print("  ⚠️ 没有结果可保存")
            return
        
        results_data = []
        for strategy in strategies:
            results_data.append({
                '策略类型': strategy['type'],
                '特征数量': strategy['feature_count'],
                '策略描述': strategy['description'],
                '胜率': f"{strategy['win_rate']*100:.1f}%",
                '平均收益率': f"{strategy['avg_return']*100:.2f}%",
                '平均盈利金额': f"{strategy['avg_profit']:.2f}元",
                '总交易次数': strategy['total_trades'],
                '综合评分': f"{strategy['score']:.6f}"
            })
        
        results_df = pd.DataFrame(results_data)
        results_df.to_excel(output_path, index=False)
        print(f"💾 保存了 {len(strategies)} 个高级多特征策略到: {output_path}")

def main():
    """主函数"""
    start_time = datetime.now()
    
    try:
        # 1. 初始化高级挖掘系统
        mining_system = AdvancedMultiFeatureStrategy(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily"
        )
        
        # 2. 加载和处理数据
        trading_df = mining_system.load_and_process_data()
        
        if len(trading_df) == 0:
            print("❌ 没有有效的交易数据")
            return
        
        # 3. 创建高级特征
        trading_df = mining_system.create_advanced_features(trading_df)
        
        # 4. 高级多特征挖掘
        advanced_strategies = mining_system.advanced_multi_feature_mining(trading_df)
        
        # 5. 显示结果
        mining_system.display_advanced_results(advanced_strategies, top_n=50)
        
        # 6. 保存结果
        output_path = f"高级多特征策略_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        mining_system.save_advanced_results(advanced_strategies, output_path)
        
        end_time = datetime.now()
        
        print(f"\n" + "="*80)
        print(f"🎉 高级多特征策略挖掘完成!")
        print(f"⏱️ 总耗时: {end_time - start_time}")
        print(f"📊 分析了 {len(trading_df):,} 笔实际交易")
        print(f"🎯 发现 {len(advanced_strategies)} 个高胜率多特征策略")
        print(f"🔍 特征组合: 3-5个特征的复杂组合")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
