#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特征重要性分析器
用于分析和选择最重要的特征，提高模型性能和可解释性

作者: AI Assistant
日期: 2025-06-30
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.linear_model import LassoCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class FeatureImportanceAnalyzer:
    """特征重要性分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.feature_importance_results = {}
        self.selected_features = {}
        self.scaler = StandardScaler()
        
    def analyze_feature_importance(self, 
                                 X_price: np.ndarray, 
                                 X_tech: np.ndarray, 
                                 y: np.ndarray,
                                 price_feature_names: List[str],
                                 tech_feature_names: List[str]) -> Dict:
        """
        综合分析特征重要性
        
        Args:
            X_price: 价格特征数据
            X_tech: 技术强度特征数据
            y: 目标变量
            price_feature_names: 价格特征名称
            tech_feature_names: 技术强度特征名称
            
        Returns:
            特征重要性分析结果
        """
        print("开始特征重要性分析...")
        
        # 合并特征数据 (使用最后一个时间步的特征)
        X_price_last = X_price[:, -1, :]  # 取最后一个时间步
        X_tech_last = X_tech[:, -1, :]
        X_combined = np.concatenate([X_price_last, X_tech_last], axis=1)
        feature_names = price_feature_names + tech_feature_names
        
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X_combined)
        
        results = {}
        
        # 1. 随机森林特征重要性
        print("1. 随机森林特征重要性分析...")
        rf_importance = self._random_forest_importance(X_scaled, y, feature_names)
        results['random_forest'] = rf_importance
        
        # 2. 梯度提升特征重要性
        print("2. 梯度提升特征重要性分析...")
        gb_importance = self._gradient_boosting_importance(X_scaled, y, feature_names)
        results['gradient_boosting'] = gb_importance
        
        # 3. Lasso特征选择
        print("3. Lasso特征选择分析...")
        lasso_importance = self._lasso_feature_selection(X_scaled, y, feature_names)
        results['lasso'] = lasso_importance
        
        # 4. 统计学特征选择
        print("4. 统计学特征选择分析...")
        statistical_importance = self._statistical_feature_selection(X_scaled, y, feature_names)
        results['statistical'] = statistical_importance
        
        # 5. 互信息特征选择
        print("5. 互信息特征选择分析...")
        mi_importance = self._mutual_info_selection(X_scaled, y, feature_names)
        results['mutual_info'] = mi_importance
        
        # 6. 综合特征重要性排名
        print("6. 综合特征重要性排名...")
        combined_ranking = self._combine_importance_rankings(results)
        results['combined_ranking'] = combined_ranking
        
        self.feature_importance_results = results
        return results
    
    def _random_forest_importance(self, X: np.ndarray, y: np.ndarray, 
                                feature_names: List[str]) -> Dict:
        """随机森林特征重要性"""
        rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X, y)
        
        importance_scores = rf.feature_importances_
        feature_importance = list(zip(feature_names, importance_scores))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        return {
            'method': 'Random Forest',
            'feature_importance': feature_importance,
            'model_score': rf.score(X, y)
        }
    
    def _gradient_boosting_importance(self, X: np.ndarray, y: np.ndarray, 
                                    feature_names: List[str]) -> Dict:
        """梯度提升特征重要性"""
        gb = GradientBoostingRegressor(n_estimators=100, random_state=42)
        gb.fit(X, y)
        
        importance_scores = gb.feature_importances_
        feature_importance = list(zip(feature_names, importance_scores))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        return {
            'method': 'Gradient Boosting',
            'feature_importance': feature_importance,
            'model_score': gb.score(X, y)
        }
    
    def _lasso_feature_selection(self, X: np.ndarray, y: np.ndarray, 
                               feature_names: List[str]) -> Dict:
        """Lasso特征选择"""
        lasso = LassoCV(cv=5, random_state=42, max_iter=2000)
        lasso.fit(X, y)
        
        # 获取非零系数的特征
        coef_abs = np.abs(lasso.coef_)
        feature_importance = list(zip(feature_names, coef_abs))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        # 选择的特征 (非零系数)
        selected_features = [name for name, coef in feature_importance if coef > 1e-6]
        
        return {
            'method': 'Lasso',
            'feature_importance': feature_importance,
            'selected_features': selected_features,
            'model_score': lasso.score(X, y),
            'alpha': lasso.alpha_
        }
    
    def _statistical_feature_selection(self, X: np.ndarray, y: np.ndarray, 
                                     feature_names: List[str]) -> Dict:
        """统计学特征选择 (F-score)"""
        selector = SelectKBest(score_func=f_regression, k='all')
        selector.fit(X, y)
        
        f_scores = selector.scores_
        feature_importance = list(zip(feature_names, f_scores))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        return {
            'method': 'F-regression',
            'feature_importance': feature_importance
        }
    
    def _mutual_info_selection(self, X: np.ndarray, y: np.ndarray, 
                             feature_names: List[str]) -> Dict:
        """互信息特征选择"""
        mi_scores = mutual_info_regression(X, y, random_state=42)
        feature_importance = list(zip(feature_names, mi_scores))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        return {
            'method': 'Mutual Information',
            'feature_importance': feature_importance
        }
    
    def _combine_importance_rankings(self, results: Dict) -> Dict:
        """综合多种方法的特征重要性排名"""
        # 收集所有特征名称
        all_features = set()
        for method_result in results.values():
            if 'feature_importance' in method_result:
                for feature_name, _ in method_result['feature_importance']:
                    all_features.add(feature_name)
        
        all_features = list(all_features)
        
        # 计算每个特征在各方法中的排名
        feature_rankings = {feature: [] for feature in all_features}
        
        for method_name, method_result in results.items():
            if 'feature_importance' in method_result:
                feature_scores = dict(method_result['feature_importance'])
                # 按重要性排序
                sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
                
                # 记录排名 (从1开始)
                for rank, (feature_name, _) in enumerate(sorted_features, 1):
                    if feature_name in feature_rankings:
                        feature_rankings[feature_name].append(rank)
        
        # 计算平均排名
        average_rankings = {}
        for feature, rankings in feature_rankings.items():
            if rankings:
                average_rankings[feature] = np.mean(rankings)
            else:
                average_rankings[feature] = len(all_features)  # 最低排名
        
        # 按平均排名排序
        combined_ranking = sorted(average_rankings.items(), key=lambda x: x[1])
        
        return {
            'method': 'Combined Ranking',
            'feature_ranking': combined_ranking,
            'top_features': [feature for feature, _ in combined_ranking[:50]]  # 前50个特征
        }
    
    def select_top_features(self, top_k: int = 50) -> List[str]:
        """
        选择最重要的前K个特征
        
        Args:
            top_k: 选择的特征数量
            
        Returns:
            选择的特征名称列表
        """
        if 'combined_ranking' not in self.feature_importance_results:
            raise ValueError("请先运行特征重要性分析")
        
        combined_result = self.feature_importance_results['combined_ranking']
        top_features = [feature for feature, _ in combined_result['feature_ranking'][:top_k]]
        
        self.selected_features['top_features'] = top_features
        return top_features
    
    def plot_feature_importance(self, method: str = 'combined_ranking', top_n: int = 20):
        """
        绘制特征重要性图
        
        Args:
            method: 分析方法
            top_n: 显示前N个特征
        """
        if method not in self.feature_importance_results:
            raise ValueError(f"方法 {method} 不存在")
        
        result = self.feature_importance_results[method]
        
        plt.figure(figsize=(12, 8))
        
        if method == 'combined_ranking':
            # 综合排名图
            features, rankings = zip(*result['feature_ranking'][:top_n])
            rankings = [1/rank for rank in rankings]  # 转换为重要性分数
            
            plt.barh(range(len(features)), rankings)
            plt.yticks(range(len(features)), features)
            plt.xlabel('重要性分数 (1/平均排名)')
            plt.title(f'综合特征重要性排名 (前{top_n}个特征)')
        else:
            # 其他方法的重要性图
            features, scores = zip(*result['feature_importance'][:top_n])
            
            plt.barh(range(len(features)), scores)
            plt.yticks(range(len(features)), features)
            plt.xlabel('重要性分数')
            plt.title(f'{result["method"]} 特征重要性 (前{top_n}个特征)')
        
        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.show()
    
    def generate_feature_report(self, output_file: str = 'feature_importance_report.txt'):
        """
        生成特征重要性分析报告
        
        Args:
            output_file: 输出文件名
        """
        if not self.feature_importance_results:
            raise ValueError("请先运行特征重要性分析")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("特征重要性分析报告\n")
            f.write("=" * 80 + "\n\n")
            
            for method_name, result in self.feature_importance_results.items():
                f.write(f"\n{method_name.upper()} 分析结果:\n")
                f.write("-" * 50 + "\n")
                
                if method_name == 'combined_ranking':
                    f.write("综合排名前20个特征:\n")
                    for i, (feature, rank) in enumerate(result['feature_ranking'][:20], 1):
                        f.write(f"{i:2d}. {feature}: 平均排名 {rank:.2f}\n")
                else:
                    if 'model_score' in result:
                        f.write(f"模型得分: {result['model_score']:.4f}\n")
                    
                    f.write("前20个重要特征:\n")
                    for i, (feature, score) in enumerate(result['feature_importance'][:20], 1):
                        f.write(f"{i:2d}. {feature}: {score:.6f}\n")
                
                f.write("\n")
        
        print(f"特征重要性分析报告已保存到: {output_file}")
