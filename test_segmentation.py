#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试连续性指标的三等分分段效果

作者: AI Assistant
日期: 2025-06-30
"""

import os
import pandas as pd
import numpy as np

def test_segmentation():
    """测试分段效果"""
    print("=" * 60)
    print("🔍 测试连续性指标三等分分段")
    print("=" * 60)
    
    # 加载技术强度数据
    tech_files = [f for f in os.listdir('tech_strength/daily') if f.endswith('.xlsx')]
    tech_dfs = []
    for file in tech_files[:1]:  # 只取一个文件测试
        df = pd.read_excel(os.path.join('tech_strength/daily', file))
        tech_dfs.append(df)
    tech_df = pd.concat(tech_dfs, ignore_index=True)
    
    print(f"测试数据: {len(tech_df)} 条记录")
    
    # 需要分段的连续性指标
    continuous_features = [
        '连续技术强度3天数', 
        '连续技术强度5天数', 
        '连续技术强度10天数',
        '成交量是前一日几倍'
    ]
    
    print("\n原始数据分布:")
    for feature in continuous_features:
        if feature in tech_df.columns:
            print(f"\n{feature}:")
            print(f"  范围: [{tech_df[feature].min():.2f}, {tech_df[feature].max():.2f}]")
            print(f"  均值: {tech_df[feature].mean():.2f}")
            print(f"  标准差: {tech_df[feature].std():.2f}")
            print(f"  唯一值数量: {tech_df[feature].nunique()}")
            
            # 显示值分布
            value_counts = tech_df[feature].value_counts().head(10)
            print(f"  前10个最常见值:")
            for val, count in value_counts.items():
                print(f"    {val}: {count} 次 ({count/len(tech_df)*100:.1f}%)")
    
    print("\n" + "=" * 60)
    print("🔧 执行三等分分段")
    print("=" * 60)
    
    # 执行分段
    for feature in continuous_features:
        if feature in tech_df.columns:
            print(f"\n处理 {feature}:")
            
            # 计算最大值和最小值
            min_val = tech_df[feature].min()
            max_val = tech_df[feature].max()
            
            print(f"  原始范围: [{min_val:.2f}, {max_val:.2f}]")
            
            # 三等分分段
            cut_points = [min_val - 0.1, 
                         min_val + (max_val - min_val) / 3,
                         min_val + 2 * (max_val - min_val) / 3,
                         max_val + 0.1]
            
            print(f"  分段点: {[f'{x:.2f}' for x in cut_points]}")
            
            tech_df[f'{feature}_分段'] = pd.cut(
                tech_df[feature], 
                bins=cut_points,
                labels=[1, 2, 3],  # 1:低, 2:中, 3:高
                include_lowest=True
            ).astype(float)
            
            # 统计分段分布
            segment_counts = tech_df[f'{feature}_分段'].value_counts().sort_index()
            total = len(tech_df)
            
            print(f"  分段结果:")
            print(f"    低值段 (1): {segment_counts.get(1.0, 0)} 个 ({segment_counts.get(1.0, 0)/total*100:.1f}%)")
            print(f"    中值段 (2): {segment_counts.get(2.0, 0)} 个 ({segment_counts.get(2.0, 0)/total*100:.1f}%)")
            print(f"    高值段 (3): {segment_counts.get(3.0, 0)} 个 ({segment_counts.get(3.0, 0)/total*100:.1f}%)")
            
            # 显示每段的原始值范围
            for segment in [1.0, 2.0, 3.0]:
                mask = tech_df[f'{feature}_分段'] == segment
                if mask.sum() > 0:
                    segment_values = tech_df.loc[mask, feature]
                    print(f"    段{int(segment)}原始值范围: [{segment_values.min():.2f}, {segment_values.max():.2f}]")
    
    print("\n" + "=" * 60)
    print("📊 分段效果总结")
    print("=" * 60)
    
    print(f"\n数据压缩效果:")
    for feature in continuous_features:
        if feature in tech_df.columns:
            original_unique = tech_df[feature].nunique()
            segmented_unique = tech_df[f'{feature}_分段'].nunique()
            compression_ratio = original_unique / segmented_unique
            
            print(f"{feature}:")
            print(f"  原始唯一值: {original_unique}")
            print(f"  分段后唯一值: {segmented_unique}")
            print(f"  压缩比: {compression_ratio:.1f}:1")
    
    print(f"\n✅ 分段处理完成!")
    print(f"   - 减少了数据稀疏性")
    print(f"   - 提高了数据挖掘效率")
    print(f"   - 保持了相对大小关系")

def main():
    test_segmentation()

if __name__ == "__main__":
    main()
