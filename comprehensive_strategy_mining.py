#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全面深入的高胜率交易策略挖掘
更全面的特征组合探索和更细致的分析

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
from itertools import combinations, product

class ComprehensiveStrategyMining:
    """全面的策略挖掘系统"""
    
    def __init__(self, tech_strength_path, stock_data_path):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.all_combinations = []
    
    def load_and_merge_data(self):
        """加载并合并数据"""
        print("=" * 80)
        print("🔍 全面深入的高胜率交易策略挖掘")
        print("📋 买卖规则: 预测上涨当日开盘买入，次日开盘卖出")
        print("=" * 80)
        
        # 加载数据
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"技术强度数据: {len(tech_df)} 条记录")
        print(f"股票交易明细: {len(stock_df)} 条记录")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 合并数据
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='inner',
            suffixes=('_tech', '_stock')
        )
        
        print(f"合并后数据: {len(merged_df)} 条记录")
        return merged_df
    
    def calculate_trading_returns(self, df):
        """计算交易收益"""
        print(f"\n💹 计算交易收益...")
        
        trading_results = []
        
        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            for i in range(len(group) - 1):
                current_row = group.iloc[i]
                next_row = group.iloc[i + 1]
                
                buy_price = current_row['开盘价']
                sell_price = next_row['开盘价']
                
                if buy_price > 0 and sell_price > 0:
                    return_rate = (sell_price - buy_price) / buy_price
                    profit_amount = sell_price - buy_price
                    
                    trading_record = {
                        '股票代码': stock_code,
                        '买入日期': current_row['日期'],
                        '卖出日期': next_row['日期'],
                        '买入价格': buy_price,
                        '卖出价格': sell_price,
                        '收益率': return_rate,
                        '盈利金额': profit_amount,
                        '是否盈利': return_rate > 0,
                        
                        # 所有技术指标
                        '技术强度': current_row['技术强度'],
                        '连续技术强度3天数': current_row.get('连续技术强度3天数', 0),
                        '连续技术强度5天数': current_row.get('连续技术强度5天数', 0),
                        '连续技术强度10天数': current_row.get('连续技术强度10天数', 0),
                        '成交量是前一日几倍': current_row.get('成交量是前一日几倍', 0),
                        '技术指标特征': current_row.get('技术指标特征', ''),
                        '趋势组合': current_row.get('趋势组合', ''),
                        '日内股票标记': current_row.get('日内股票标记', ''),
                        
                        # 价格相关
                        '开盘价': current_row['开盘价'],
                        '最高价': current_row['最高价'],
                        '最低价': current_row['最低价'],
                        '收盘价': current_row.get('收盘价_stock', current_row.get('收盘价', 0)),
                        '前收盘价': current_row.get('前收盘价', 0),
                        '成交量': current_row.get('成交量_stock', current_row.get('成交量', 0)),
                        '成交额': current_row.get('成交额', 0),
                        '换手率': current_row.get('换手率', 0),
                        '涨跌幅': current_row.get('涨跌幅_stock', current_row.get('涨跌幅', 0)),
                        
                        # 市场状态
                        '复权状态': current_row.get('复权状态', ''),
                        '交易状态': current_row.get('交易状态', ''),
                        '是否ST股': current_row.get('是否ST股', ''),
                    }
                    
                    trading_results.append(trading_record)
        
        trading_df = pd.DataFrame(trading_results)
        print(f"  生成交易记录: {len(trading_df)} 笔交易")
        
        if len(trading_df) > 0:
            total_trades = len(trading_df)
            profitable_trades = trading_df['是否盈利'].sum()
            win_rate = profitable_trades / total_trades
            avg_return = trading_df['收益率'].mean()
            
            print(f"  总交易次数: {total_trades:,}")
            print(f"  盈利交易: {profitable_trades:,}")
            print(f"  整体胜率: {win_rate:.3f} ({win_rate*100:.1f}%)")
            print(f"  平均收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")
        
        return trading_df
    
    def create_comprehensive_features(self, df):
        """创建全面的特征"""
        print(f"\n🔧 创建全面的特征体系...")
        
        # 1. 技术强度的多种分段
        if '技术强度' in df.columns:
            # 二分法
            df['技术强度_二分'] = pd.cut(df['技术强度'], bins=2, labels=[1, 2]).astype(float)
            # 三分法
            df['技术强度_三分'] = pd.cut(df['技术强度'], bins=3, labels=[1, 2, 3]).astype(float)
            # 五分法
            df['技术强度_五分'] = pd.cut(df['技术强度'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
            # 十分法
            df['技术强度_十分'] = pd.cut(df['技术强度'], bins=10, labels=range(1, 11)).astype(float)
            
            # 基于分位数的分段
            quantiles = df['技术强度'].quantile([0.2, 0.4, 0.6, 0.8]).values
            df['技术强度_分位数'] = pd.cut(
                df['技术强度'], 
                bins=[df['技术强度'].min()-1] + list(quantiles) + [df['技术强度'].max()+1],
                labels=[1, 2, 3, 4, 5]
            ).astype(float)
        
        # 2. 连续技术强度的多种分段
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500},
            '连续技术强度10天数': {'min': 28, 'max': 956}
        }
        
        for indicator, info in continuous_indicators.items():
            if indicator in df.columns:
                min_val = info['min']
                max_val = info['max']
                
                # 三等分
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                df[f'{indicator}_三分'] = pd.cut(
                    df[indicator],
                    bins=[min_val - 0.1, cut1, cut2, max_val + 0.1],
                    labels=[1, 2, 3]
                ).astype(float)
                
                # 五等分
                cuts = [min_val - 0.1]
                for i in range(1, 5):
                    cuts.append(min_val + i * range_size / 5)
                cuts.append(max_val + 0.1)
                df[f'{indicator}_五分'] = pd.cut(
                    df[indicator], bins=cuts, labels=[1, 2, 3, 4, 5]
                ).astype(float)
                
                # 分位数分段
                if df[indicator].nunique() > 10:
                    quantiles = df[indicator].quantile([0.25, 0.5, 0.75]).values
                    df[f'{indicator}_分位数'] = pd.cut(
                        df[indicator],
                        bins=[min_val-1] + list(quantiles) + [max_val+1],
                        labels=[1, 2, 3, 4]
                    ).astype(float)
        
        # 3. 成交量的多种分段
        if '成交量是前一日几倍' in df.columns:
            # 基础分段
            df['成交量_基础分段'] = pd.cut(
                df['成交量是前一日几倍'],
                bins=[0, 1.0, 2.0, 4.0],
                labels=[1, 2, 3]
            ).astype(float)
            
            # 细分段
            df['成交量_细分段'] = pd.cut(
                df['成交量是前一日几倍'],
                bins=[0, 0.8, 1.2, 1.8, 2.5, 4.0],
                labels=[1, 2, 3, 4, 5]
            ).astype(float)
            
            # 极值分段
            df['成交量_极值分段'] = pd.cut(
                df['成交量是前一日几倍'],
                bins=[0, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0],
                labels=[1, 2, 3, 4, 5, 6, 7]
            ).astype(float)
        
        # 4. 价格相关特征
        if all(col in df.columns for col in ['开盘价', '最高价', '最低价', '收盘价']):
            # 价格波动率
            df['价格波动率'] = (df['最高价'] - df['最低价']) / df['收盘价']
            df['价格波动率_分段'] = pd.cut(df['价格波动率'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
            
            # 开盘缺口
            if '前收盘价' in df.columns:
                df['开盘缺口'] = (df['开盘价'] - df['前收盘价']) / df['前收盘价']
                df['开盘缺口_分段'] = pd.cut(df['开盘缺口'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
            
            # 收盘强度
            price_range = df['最高价'] - df['最低价']
            price_range = price_range.replace(0, 1e-8)
            df['收盘强度'] = (df['收盘价'] - df['最低价']) / price_range
            df['收盘强度_分段'] = pd.cut(df['收盘强度'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 5. 换手率分段
        if '换手率' in df.columns:
            df['换手率_分段'] = pd.cut(df['换手率'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 6. 涨跌幅分段
        if '涨跌幅' in df.columns:
            df['涨跌幅_分段'] = pd.cut(df['涨跌幅'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 7. 组合特征
        if '连续技术强度3天数_三分' in df.columns and '连续技术强度5天数_三分' in df.columns:
            df['短中期组合_三分'] = df['连续技术强度3天数_三分'] * 10 + df['连续技术强度5天数_三分']
        
        if '连续技术强度5天数_三分' in df.columns and '连续技术强度10天数_三分' in df.columns:
            df['中长期组合_三分'] = df['连续技术强度5天数_三分'] * 10 + df['连续技术强度10天数_三分']
        
        if '技术强度_三分' in df.columns and '成交量_基础分段' in df.columns:
            df['技术强度成交量组合'] = df['技术强度_三分'] * 10 + df['成交量_基础分段']
        
        print(f"  创建特征完成，当前特征数: {len(df.columns)}")
        return df
    
    def comprehensive_single_feature_analysis(self, df, min_trades=30, min_win_rate=0.65):
        """全面的单特征分析"""
        print(f"\n📊 全面单特征分析 (最少{min_trades}笔交易, 胜率≥{min_win_rate*100:.0f}%)...")
        
        single_feature_results = []
        
        # 获取所有可能的特征
        feature_columns = [col for col in df.columns if any(keyword in col for keyword in [
            '技术强度', '连续技术强度', '成交量', '价格波动率', '开盘缺口', '收盘强度', 
            '换手率', '涨跌幅', '组合'
        ]) and col not in ['收益率', '盈利金额', '是否盈利']]
        
        print(f"  分析 {len(feature_columns)} 个特征...")
        
        for feature in feature_columns:
            if feature in df.columns:
                unique_values = sorted([v for v in df[feature].unique() if pd.notna(v)])
                
                for value in unique_values:
                    subset = df[df[feature] == value]
                    if len(subset) >= min_trades:
                        win_rate = subset['是否盈利'].mean()
                        avg_return = subset['收益率'].mean()
                        avg_profit = subset['盈利金额'].mean()
                        total_trades = len(subset)
                        profitable_trades = subset['是否盈利'].sum()
                        
                        if win_rate >= min_win_rate:
                            single_feature_results.append({
                                'feature': feature,
                                'value': value,
                                'win_rate': win_rate,
                                'avg_return': avg_return,
                                'avg_profit': avg_profit,
                                'total_trades': total_trades,
                                'profitable_trades': profitable_trades,
                                'description': f'{feature}={value}',
                                'score': win_rate * avg_return,
                                'type': 'single'
                            })
        
        # 按评分排序
        single_feature_results.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"  发现 {len(single_feature_results)} 个高胜率单特征组合")
        
        # 显示前20个
        if single_feature_results:
            print(f"\n  前20个最佳单特征组合:")
            for i, result in enumerate(single_feature_results[:20], 1):
                print(f"    {i:2d}. {result['description']}: "
                      f"胜率{result['win_rate']:.3f}({result['win_rate']*100:.1f}%) "
                      f"收益{result['avg_return']:.4f}({result['avg_return']*100:.2f}%) "
                      f"交易{result['total_trades']}笔")
        
        return single_feature_results

    def comprehensive_multi_feature_analysis(self, df, min_trades=20, min_win_rate=0.70):
        """全面的多特征组合分析"""
        print(f"\n🔍 全面多特征组合分析 (最少{min_trades}笔交易, 胜率≥{min_win_rate*100:.0f}%)...")

        multi_feature_results = []

        # 定义核心特征组
        core_features = {
            'tech_strength': ['技术强度', '技术强度_三分', '技术强度_五分'],
            'continuous_3d': ['连续技术强度3天数_三分', '连续技术强度3天数_五分'],
            'continuous_5d': ['连续技术强度5天数_三分', '连续技术强度5天数_五分'],
            'continuous_10d': ['连续技术强度10天数_三分', '连续技术强度10天数_五分'],
            'volume': ['成交量是前一日几倍', '成交量_基础分段', '成交量_细分段', '成交量_极值分段'],
            'price_features': ['价格波动率_分段', '开盘缺口_分段', '收盘强度_分段'],
            'market_features': ['换手率_分段', '涨跌幅_分段'],
            'combinations': ['短中期组合_三分', '中长期组合_三分', '技术强度成交量组合']
        }

        # 双特征组合分析
        print(f"  分析双特征组合...")
        feature_pairs = [
            ('tech_strength', 'volume'),
            ('tech_strength', 'continuous_3d'),
            ('tech_strength', 'continuous_5d'),
            ('tech_strength', 'price_features'),
            ('continuous_3d', 'continuous_5d'),
            ('continuous_3d', 'volume'),
            ('continuous_5d', 'volume'),
            ('volume', 'price_features'),
            ('tech_strength', 'market_features'),
            ('volume', 'market_features'),
        ]

        for group1, group2 in feature_pairs:
            features1 = [f for f in core_features[group1] if f in df.columns]
            features2 = [f for f in core_features[group2] if f in df.columns]

            for feature1 in features1:
                for feature2 in features2:
                    values1 = sorted([v for v in df[feature1].unique() if pd.notna(v)])[:8]
                    values2 = sorted([v for v in df[feature2].unique() if pd.notna(v)])[:8]

                    combo_count = 0
                    for val1 in values1:
                        for val2 in values2:
                            subset = df[(df[feature1] == val1) & (df[feature2] == val2)]
                            if len(subset) >= min_trades:
                                win_rate = subset['是否盈利'].mean()
                                avg_return = subset['收益率'].mean()
                                avg_profit = subset['盈利金额'].mean()
                                total_trades = len(subset)
                                profitable_trades = subset['是否盈利'].sum()

                                if win_rate >= min_win_rate:
                                    multi_feature_results.append({
                                        'features': {feature1: val1, feature2: val2},
                                        'win_rate': win_rate,
                                        'avg_return': avg_return,
                                        'avg_profit': avg_profit,
                                        'total_trades': total_trades,
                                        'profitable_trades': profitable_trades,
                                        'description': f'{feature1}={val1}, {feature2}={val2}',
                                        'score': win_rate * avg_return,
                                        'type': 'double'
                                    })
                                    combo_count += 1

                                    if combo_count >= 10:  # 限制每对特征的组合数量
                                        break
                            if combo_count >= 10:
                                break
                        if combo_count >= 10:
                            break

        # 三特征组合分析 (选择性分析)
        print(f"  分析三特征组合...")
        triple_combinations = [
            ('tech_strength', 'continuous_3d', 'volume'),
            ('tech_strength', 'continuous_5d', 'volume'),
            ('tech_strength', 'volume', 'price_features'),
            ('continuous_3d', 'continuous_5d', 'volume'),
        ]

        for group1, group2, group3 in triple_combinations:
            features1 = [f for f in core_features[group1] if f in df.columns][:2]
            features2 = [f for f in core_features[group2] if f in df.columns][:2]
            features3 = [f for f in core_features[group3] if f in df.columns][:2]

            for feature1 in features1:
                for feature2 in features2:
                    for feature3 in features3:
                        values1 = sorted([v for v in df[feature1].unique() if pd.notna(v)])[:5]
                        values2 = sorted([v for v in df[feature2].unique() if pd.notna(v)])[:5]
                        values3 = sorted([v for v in df[feature3].unique() if pd.notna(v)])[:5]

                        combo_count = 0
                        for val1 in values1:
                            for val2 in values2:
                                for val3 in values3:
                                    subset = df[(df[feature1] == val1) &
                                              (df[feature2] == val2) &
                                              (df[feature3] == val3)]
                                    if len(subset) >= min_trades:
                                        win_rate = subset['是否盈利'].mean()
                                        avg_return = subset['收益率'].mean()
                                        avg_profit = subset['盈利金额'].mean()
                                        total_trades = len(subset)
                                        profitable_trades = subset['是否盈利'].sum()

                                        if win_rate >= min_win_rate:
                                            multi_feature_results.append({
                                                'features': {feature1: val1, feature2: val2, feature3: val3},
                                                'win_rate': win_rate,
                                                'avg_return': avg_return,
                                                'avg_profit': avg_profit,
                                                'total_trades': total_trades,
                                                'profitable_trades': profitable_trades,
                                                'description': f'{feature1}={val1}, {feature2}={val2}, {feature3}={val3}',
                                                'score': win_rate * avg_return,
                                                'type': 'triple'
                                            })
                                            combo_count += 1

                                            if combo_count >= 5:
                                                break
                                    if combo_count >= 5:
                                        break
                                if combo_count >= 5:
                                    break
                            if combo_count >= 5:
                                break

        # 按评分排序
        multi_feature_results.sort(key=lambda x: x['score'], reverse=True)

        print(f"  发现 {len(multi_feature_results)} 个高胜率多特征组合")

        # 显示前30个
        if multi_feature_results:
            print(f"\n  前30个最佳多特征组合:")
            for i, result in enumerate(multi_feature_results[:30], 1):
                print(f"    {i:2d}. {result['description']}: "
                      f"胜率{result['win_rate']:.3f}({result['win_rate']*100:.1f}%) "
                      f"收益{result['avg_return']:.4f}({result['avg_return']*100:.2f}%) "
                      f"交易{result['total_trades']}笔 ({result['type']})")

        return multi_feature_results

    def advanced_pattern_analysis(self, df, min_trades=15, min_win_rate=0.75):
        """高级模式分析"""
        print(f"\n🎯 高级模式分析 (最少{min_trades}笔交易, 胜率≥{min_win_rate*100:.0f}%)...")

        advanced_patterns = []

        # 1. 极值组合分析
        print(f"  分析极值组合...")
        if '技术强度' in df.columns and '成交量是前一日几倍' in df.columns:
            # 技术强度极值 + 成交量极值
            tech_extremes = [28, 100]  # 最低和最高技术强度
            volume_extremes = [0.5, 3.0, 3.5]  # 极低和极高成交量

            for tech_val in tech_extremes:
                for vol_val in volume_extremes:
                    subset = df[(df['技术强度'] == tech_val) & (df['成交量是前一日几倍'] == vol_val)]
                    if len(subset) >= min_trades:
                        win_rate = subset['是否盈利'].mean()
                        avg_return = subset['收益率'].mean()

                        if win_rate >= min_win_rate or win_rate <= (1 - min_win_rate):  # 极高胜率或极低胜率
                            advanced_patterns.append({
                                'pattern_type': 'extreme_combo',
                                'description': f'技术强度={tech_val}, 成交量={vol_val}倍 (极值组合)',
                                'win_rate': win_rate,
                                'avg_return': avg_return,
                                'total_trades': len(subset),
                                'score': abs(win_rate - 0.5) * abs(avg_return)
                            })

        # 2. 趋势一致性分析
        print(f"  分析趋势一致性...")
        if all(col in df.columns for col in ['连续技术强度3天数_三分', '连续技术强度5天数_三分', '技术强度_三分']):
            # 所有指标都指向同一方向
            for direction in [1, 3]:  # 1=低/弱, 3=高/强
                subset = df[(df['连续技术强度3天数_三分'] == direction) &
                           (df['连续技术强度5天数_三分'] == direction) &
                           (df['技术强度_三分'] == direction)]
                if len(subset) >= min_trades:
                    win_rate = subset['是否盈利'].mean()
                    avg_return = subset['收益率'].mean()

                    if win_rate >= min_win_rate:
                        direction_name = "强势一致" if direction == 3 else "弱势一致"
                        advanced_patterns.append({
                            'pattern_type': 'trend_consistency',
                            'description': f'{direction_name} (3天+5天+技术强度={direction})',
                            'win_rate': win_rate,
                            'avg_return': avg_return,
                            'total_trades': len(subset),
                            'score': win_rate * avg_return
                        })

        # 3. 反转模式分析
        print(f"  分析反转模式...")
        if all(col in df.columns for col in ['技术强度', '连续技术强度3天数_三分', '成交量_基础分段']):
            # 低技术强度 + 高成交量 (可能的反转信号)
            subset = df[(df['技术强度'] <= 42) & (df['成交量_基础分段'] >= 2)]
            if len(subset) >= min_trades:
                win_rate = subset['是否盈利'].mean()
                avg_return = subset['收益率'].mean()

                if win_rate >= min_win_rate:
                    advanced_patterns.append({
                        'pattern_type': 'reversal',
                        'description': '低技术强度+高成交量 (反转模式)',
                        'win_rate': win_rate,
                        'avg_return': avg_return,
                        'total_trades': len(subset),
                        'score': win_rate * avg_return
                    })

        # 4. 突破模式分析
        print(f"  分析突破模式...")
        if all(col in df.columns for col in ['技术强度', '价格波动率_分段', '成交量_基础分段']):
            # 高技术强度 + 高波动率 + 高成交量
            subset = df[(df['技术强度'] >= 85) &
                       (df['价格波动率_分段'] >= 4) &
                       (df['成交量_基础分段'] >= 2)]
            if len(subset) >= min_trades:
                win_rate = subset['是否盈利'].mean()
                avg_return = subset['收益率'].mean()

                if win_rate >= min_win_rate:
                    advanced_patterns.append({
                        'pattern_type': 'breakout',
                        'description': '高技术强度+高波动率+高成交量 (突破模式)',
                        'win_rate': win_rate,
                        'avg_return': avg_return,
                        'total_trades': len(subset),
                        'score': win_rate * avg_return
                    })

        # 按评分排序
        advanced_patterns.sort(key=lambda x: x['score'], reverse=True)

        print(f"  发现 {len(advanced_patterns)} 个高级模式")

        if advanced_patterns:
            print(f"\n  高级模式分析结果:")
            for i, pattern in enumerate(advanced_patterns, 1):
                print(f"    {i:2d}. {pattern['description']}: "
                      f"胜率{pattern['win_rate']:.3f}({pattern['win_rate']*100:.1f}%) "
                      f"收益{pattern['avg_return']:.4f}({pattern['avg_return']*100:.2f}%) "
                      f"交易{pattern['total_trades']}笔")

        return advanced_patterns

    def save_comprehensive_results(self, single_results, multi_results, advanced_results, output_path):
        """保存全面的分析结果"""
        print(f"\n💾 保存全面分析结果...")

        # 合并所有结果
        all_results = []

        # 添加单特征结果
        for result in single_results:
            all_results.append({
                '组合类型': result['type'],
                '组合描述': result['description'],
                '胜率': f"{result['win_rate']*100:.1f}%",
                '平均收益率': f"{result['avg_return']*100:.2f}%",
                '平均盈利': f"{result['avg_profit']:.2f}元",
                '总交易次数': result['total_trades'],
                '盈利次数': result['profitable_trades'],
                '综合评分': f"{result['score']:.6f}"
            })

        # 添加多特征结果
        for result in multi_results:
            all_results.append({
                '组合类型': result['type'],
                '组合描述': result['description'],
                '胜率': f"{result['win_rate']*100:.1f}%",
                '平均收益率': f"{result['avg_return']*100:.2f}%",
                '平均盈利': f"{result['avg_profit']:.2f}元",
                '总交易次数': result['total_trades'],
                '盈利次数': result['profitable_trades'],
                '综合评分': f"{result['score']:.6f}"
            })

        # 添加高级模式结果
        for result in advanced_results:
            all_results.append({
                '组合类型': result['pattern_type'],
                '组合描述': result['description'],
                '胜率': f"{result['win_rate']*100:.1f}%",
                '平均收益率': f"{result['avg_return']*100:.2f}%",
                '平均盈利': 'N/A',
                '总交易次数': result['total_trades'],
                '盈利次数': 'N/A',
                '综合评分': f"{result['score']:.6f}"
            })

        # 转换为DataFrame并保存
        results_df = pd.DataFrame(all_results)
        results_df.to_excel(output_path, index=False)

        print(f"  保存了 {len(all_results)} 个高胜率组合到: {output_path}")

def main():
    """主函数"""
    start_time = datetime.now()

    try:
        # 1. 初始化全面挖掘系统
        mining_system = ComprehensiveStrategyMining(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily"
        )

        # 2. 加载和合并数据
        merged_df = mining_system.load_and_merge_data()

        # 3. 计算交易收益
        trading_df = mining_system.calculate_trading_returns(merged_df)

        if len(trading_df) == 0:
            print("❌ 没有有效的交易数据")
            return

        # 4. 创建全面的特征体系
        trading_df = mining_system.create_comprehensive_features(trading_df)

        # 5. 全面的单特征分析
        single_results = mining_system.comprehensive_single_feature_analysis(trading_df)

        # 6. 全面的多特征组合分析
        multi_results = mining_system.comprehensive_multi_feature_analysis(trading_df)

        # 7. 高级模式分析
        advanced_results = mining_system.advanced_pattern_analysis(trading_df)

        # 8. 保存全面结果
        output_path = f"全面高胜率策略挖掘_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        mining_system.save_comprehensive_results(single_results, multi_results, advanced_results, output_path)

        end_time = datetime.now()

        print(f"\n" + "="*80)
        print(f"🎉 全面深入的高胜率策略挖掘完成!")
        print(f"⏱️ 总耗时: {end_time - start_time}")
        print(f"📊 分析了 {len(trading_df):,} 笔实际交易")
        print(f"🎯 发现单特征组合: {len(single_results)} 个")
        print(f"🔍 发现多特征组合: {len(multi_results)} 个")
        print(f"🎯 发现高级模式: {len(advanced_results)} 个")
        print(f"📁 总计发现: {len(single_results) + len(multi_results) + len(advanced_results)} 个高胜率策略")
        print("="*80)

    except Exception as e:
        print(f"❌ 全面分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
