#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
信号等级预测系统
为预测文件资料中的数据生成带有明确信号等级标记的预测结果

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')
from sklearn.preprocessing import LabelEncoder

class SignalLevelPredictor:
    """信号等级预测器"""
    
    def __init__(self):
        self.label_encoders = {}
        
        # 高胜率预测规则 (使用明确组合名称和信号等级)
        self.prediction_rules = [
            # 🎯 完美信号 (≥95%)
            {
                'name': '[完美信号] 连续强势+趋势确认+高涨跌幅组合',
                'signal_level': '🎯 完美信号',
                'condition': lambda row: (row.get('连续技术强度5天数_三分', 0) == 3.0 and
                                        row.get('趋势组合_编码', 0) == 2 and
                                        row.get('涨跌幅_分段', 0) == 5.0),
                'probability': 0.988,
                'expected_return': 0.1811,
                'confidence': 'PERFECT',
                'description': '连续技术强度5天高段 + 趋势组合编码2 + 涨跌幅分段5'
            },
            
            # 🥇 黄金信号 (85-95%)
            {
                'name': '[黄金信号] 技术强度满分组合',
                'signal_level': '🥇 黄金信号',
                'condition': lambda row: row.get('技术强度', 0) == 100,
                'probability': 0.890,
                'expected_return': 0.0343,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 (满分技术强度)'
            },
            
            # 💎 钻石级组合 (90-95%)
            {
                'name': '[钻石组合] 满分技术+超高成交量组合',
                'signal_level': '💎 钻石级组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 3.5),
                'probability': 0.891,
                'expected_return': 0.0661,
                'confidence': 'EXCELLENT',
                'description': '技术强度100 + 成交量3.5倍 (超高热度)'
            },
            {
                'name': '[白金组合] 满分技术+高成交量组合',
                'signal_level': '💎 钻石级组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 3.0),
                'probability': 0.912,
                'expected_return': 0.0543,
                'confidence': 'EXCELLENT',
                'description': '技术强度100 + 成交量3.0倍 (高热度)'
            },
            {
                'name': '[银质组合] 满分技术+中高成交量组合',
                'signal_level': '💎 钻石级组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 2.5),
                'probability': 0.924,
                'expected_return': 0.0504,
                'confidence': 'EXCELLENT',
                'description': '技术强度100 + 成交量2.5倍 (中高热度)'
            },
            
            # 🥈 稳健信号 (80-85%)
            {
                'name': '[稳健信号] 高技术强度组合',
                'signal_level': '🥈 稳健信号',
                'condition': lambda row: row.get('技术强度', 0) == 85,
                'probability': 0.813,
                'expected_return': 0.0203,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 (高技术强度)'
            },
            
            # 🔥 热点信号 (80-90%)
            {
                'name': '[热点组合] 高技术+超高成交量组合',
                'signal_level': '🔥 热点信号',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 3.5),
                'probability': 0.849,
                'expected_return': 0.0463,
                'confidence': 'VERY_GOOD',
                'description': '技术强度85 + 成交量3.5倍 (热点股)'
            },
            {
                'name': '[活跃组合] 高技术+高成交量组合',
                'signal_level': '🔥 热点信号',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 3.0),
                'probability': 0.876,
                'expected_return': 0.0398,
                'confidence': 'VERY_GOOD',
                'description': '技术强度85 + 成交量3.0倍 (活跃股)'
            },
            
            # 📊 放量信号 (75-80%)
            {
                'name': '[放量信号] 超高成交量组合',
                'signal_level': '📊 放量信号',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 3.5,
                'probability': 0.786,
                'expected_return': 0.0402,
                'confidence': 'GOOD',
                'description': '成交量3.5倍 (超高放量)'
            },
            {
                'name': '[活跃信号] 高成交量组合',
                'signal_level': '📊 放量信号',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 3.0,
                'probability': 0.808,
                'expected_return': 0.0340,
                'confidence': 'GOOD',
                'description': '成交量3.0倍 (高放量)'
            },
            
            # 🥉 基础信号 (70-75%)
            {
                'name': '[基础信号] 中等技术强度组合',
                'signal_level': '🥉 基础信号',
                'condition': lambda row: row.get('技术强度', 0) == 71,
                'probability': 0.744,
                'expected_return': 0.0074,
                'confidence': 'MODERATE',
                'description': '技术强度=71 (中等技术强度)'
            }
        ]
    
    def create_features(self, df):
        """创建预测特征"""
        # 处理分类特征
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for feature in categorical_features:
            if feature in df.columns:
                df[feature] = df[feature].fillna('Unknown')
                if feature not in self.label_encoders:
                    self.label_encoders[feature] = LabelEncoder()
                    df[f'{feature}_编码'] = self.label_encoders[feature].fit_transform(df[feature].astype(str))
        
        # 连续技术强度分段
        if '连续技术强度5天数' in df.columns:
            min_val, max_val = 28, 500
            range_size = max_val - min_val
            cut1 = min_val + range_size / 3
            cut2 = min_val + 2 * range_size / 3
            df['连续技术强度5天数_三分'] = pd.cut(
                df['连续技术强度5天数'], 
                bins=[min_val-1, cut1, cut2, max_val+1], 
                labels=[1, 2, 3]
            ).astype(float)
        
        # 涨跌幅分段
        if '涨跌幅' in df.columns:
            df['涨跌幅_分段'] = pd.cut(df['涨跌幅'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        return df
    
    def predict_stock(self, row):
        """预测单只股票"""
        # 按优先级检查规则 (从高胜率到低胜率)
        for rule in self.prediction_rules:
            try:
                if rule['condition'](row):
                    return {
                        'rule_name': rule['name'],
                        'signal_level': rule['signal_level'],
                        'predicted_probability': rule['probability'],
                        'predicted_return': rule['expected_return'],
                        'confidence': rule['confidence'],
                        'description': rule['description'],
                        'predicted_up': rule['probability'] > 0.5
                    }
            except:
                continue
        
        # 默认预测 (无匹配规则)
        return {
            'rule_name': '[无匹配] 市场平均水平',
            'signal_level': '➡️ 普通信号',
            'predicted_probability': 0.536,
            'predicted_return': 0.0022,
            'confidence': 'NEUTRAL',
            'description': '无特定规则匹配，使用市场平均水平',
            'predicted_up': True
        }
    
    def predict_file(self, file_path):
        """预测单个文件"""
        print("=" * 80)
        print(f"🔮 信号等级预测: {os.path.basename(file_path)}")
        print("=" * 80)
        
        # 1. 加载数据
        try:
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                print(f"❌ 不支持的文件格式")
                return None
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return None
        
        print(f"📂 加载数据: {len(df)} 只股票")
        
        # 2. 创建特征
        df = self.create_features(df)
        
        # 3. 生成预测
        print(f"🎯 生成信号等级预测...")
        predictions = []
        for _, row in df.iterrows():
            pred = self.predict_stock(row)
            predictions.append(pred)
        
        # 4. 添加预测结果
        df['信号等级'] = [p['signal_level'] for p in predictions]
        df['预测规则'] = [p['rule_name'] for p in predictions]
        df['上涨概率'] = [p['predicted_probability'] for p in predictions]
        df['上涨概率_百分比'] = [p['predicted_probability'] * 100 for p in predictions]
        df['预期收益率'] = [p['predicted_return'] for p in predictions]
        df['预期收益率_百分比'] = [p['predicted_return'] * 100 for p in predictions]
        df['置信等级'] = [p['confidence'] for p in predictions]
        df['预测依据'] = [p['description'] for p in predictions]
        df['预测上涨'] = [p['predicted_up'] for p in predictions]
        
        return df, predictions
    
    def analyze_signal_distribution(self, df, predictions):
        """分析信号分布"""
        print(f"\n📊 信号等级分布分析:")
        
        total = len(df)
        
        # 按信号等级统计
        signal_counts = df['信号等级'].value_counts()
        
        print(f"\n  🎯 信号等级分布:")
        for signal_level, count in signal_counts.items():
            percentage = count / total * 100
            avg_prob = df[df['信号等级'] == signal_level]['上涨概率'].mean()
            avg_return = df[df['信号等级'] == signal_level]['预期收益率'].mean()
            print(f"    {signal_level}: {count} 只 ({percentage:.1f}%)")
            print(f"      平均概率: {avg_prob:.1%}, 平均预期收益: {avg_return:.2%}")
        
        # 高价值信号统计
        print(f"\n  💎 高价值信号统计:")
        perfect_signals = df[df['信号等级'] == '🎯 完美信号']
        golden_signals = df[df['信号等级'] == '🥇 黄金信号']
        diamond_signals = df[df['信号等级'] == '💎 钻石级组合']
        stable_signals = df[df['信号等级'] == '🥈 稳健信号']
        
        print(f"    🎯 完美信号: {len(perfect_signals)} 只")
        print(f"    🥇 黄金信号: {len(golden_signals)} 只")
        print(f"    💎 钻石级组合: {len(diamond_signals)} 只")
        print(f"    🥈 稳健信号: {len(stable_signals)} 只")
        print(f"    高价值信号总计: {len(perfect_signals) + len(golden_signals) + len(diamond_signals) + len(stable_signals)} 只")
        
        return df
    
    def show_top_recommendations(self, df, top_n=20):
        """显示顶级推荐"""
        print(f"\n🏆 顶级推荐 (按信号等级排序，前{top_n}只):")
        
        # 定义信号等级优先级
        signal_priority = {
            '🎯 完美信号': 1,
            '💎 钻石级组合': 2,
            '🥇 黄金信号': 3,
            '🔥 热点信号': 4,
            '🥈 稳健信号': 5,
            '📊 放量信号': 6,
            '🥉 基础信号': 7,
            '➡️ 普通信号': 8
        }
        
        # 添加优先级列并排序
        df['信号优先级'] = df['信号等级'].map(signal_priority)
        top_stocks = df.nsmallest(top_n, ['信号优先级', '上涨概率'], keep='first')
        
        for idx, (_, row) in enumerate(top_stocks.iterrows(), 1):
            stock_code = row.get('股票代码', 'Unknown')
            stock_name = row.get('股票名称', '')
            signal_level = row['信号等级']
            probability = row['上涨概率_百分比']
            expected_return = row['预期收益率_百分比']
            rule_name = row['预测规则']
            description = row['预测依据']
            
            print(f"  {idx:2d}. {signal_level} - {stock_code} {stock_name}")
            print(f"      上涨概率: {probability:.1f}% | 预期收益: {expected_return:.2f}%")
            print(f"      预测规则: {rule_name}")
            print(f"      预测依据: {description}")
            print()
    
    def save_predictions(self, df, output_path):
        """保存预测结果"""
        # 选择要保存的列
        output_columns = [
            '股票代码', '股票名称', '日期',
            '信号等级', '预测规则', '上涨概率_百分比', '预期收益率_百分比',
            '置信等级', '预测依据',
            '技术强度', '成交量是前一日几倍', '日内股票标记',
            '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数'
        ]
        
        available_columns = [col for col in output_columns if col in df.columns]
        
        # 重命名列
        column_rename = {
            '上涨概率_百分比': '明天上涨概率(%)',
            '预期收益率_百分比': '预期收益率(%)',
            '置信等级': '置信等级',
            '预测规则': '使用的预测规则',
            '预测依据': '预测依据'
        }
        
        output_df = df[available_columns].copy()
        output_df = output_df.rename(columns=column_rename)
        
        # 按信号等级和概率排序
        signal_priority = {
            '🎯 完美信号': 1, '💎 钻石级组合': 2, '🥇 黄金信号': 3,
            '🔥 热点信号': 4, '🥈 稳健信号': 5, '📊 放量信号': 6,
            '🥉 基础信号': 7, '➡️ 普通信号': 8
        }
        output_df['信号优先级'] = output_df['信号等级'].map(signal_priority)
        output_df = output_df.sort_values(['信号优先级', '明天上涨概率(%)'], ascending=[True, False])
        output_df = output_df.drop('信号优先级', axis=1)
        
        if output_path.endswith('.xlsx'):
            output_df.to_excel(output_path, index=False)
        else:
            output_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        print(f"💾 信号等级预测结果已保存到: {output_path}")

def main():
    """主函数"""
    predictor = SignalLevelPredictor()
    
    # 预测用户数据目录中的文件
    prediction_dir = "预测文件资料"
    
    if not os.path.exists(prediction_dir):
        print(f"❌ 预测数据目录不存在: {prediction_dir}")
        return
    
    # 获取目录中的所有Excel文件 (排除已生成的预测文件)
    files = [f for f in os.listdir(prediction_dir) 
             if f.endswith(('.xlsx', '.xls', '.csv')) 
             and not f.endswith('_上涨概率预测.xlsx') 
             and not f.endswith('_高级多特征预测.xlsx')
             and not f.endswith('_信号等级预测.xlsx')
             and not f.startswith('~')]
    
    if not files:
        print(f"❌ 在 {prediction_dir} 目录中未找到数据文件")
        return
    
    print(f"📂 发现 {len(files)} 个数据文件:")
    for i, file in enumerate(files, 1):
        print(f"  {i}. {file}")
    
    # 处理每个文件
    for file_name in files:
        file_path = os.path.join(prediction_dir, file_name)
        
        print(f"\n" + "="*80)
        print(f"📊 信号等级预测: {file_name}")
        print(f"="*80)
        
        try:
            # 预测
            df, predictions = predictor.predict_file(file_path)
            
            if df is not None:
                # 分析信号分布
                df = predictor.analyze_signal_distribution(df, predictions)
                
                # 显示顶级推荐
                predictor.show_top_recommendations(df, top_n=15)
                
                # 保存结果
                if file_path.endswith('.xlsx'):
                    output_path = file_path.replace('.xlsx', '_信号等级预测.xlsx')
                elif file_path.endswith('.xls'):
                    output_path = file_path.replace('.xls', '_信号等级预测.xlsx')
                elif file_path.endswith('.csv'):
                    output_path = file_path.replace('.csv', '_信号等级预测.csv')
                else:
                    output_path = file_path + '_信号等级预测.xlsx'
                
                predictor.save_predictions(df, output_path)
                
                print(f"✅ {file_name} 信号等级预测完成！")
            
        except Exception as e:
            print(f"❌ 预测 {file_name} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    print(f"\n🎉 所有文件的信号等级预测完成！")
    print(f"🎯 重点关注完美信号、黄金信号和钻石级组合")

if __name__ == "__main__":
    main()
