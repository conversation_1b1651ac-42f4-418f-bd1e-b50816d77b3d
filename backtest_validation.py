import pandas as pd
import numpy as np
import glob
import re
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class BacktestValidation:
    """回测验证系统 - 确保逻辑准确和数据价值挖掘"""
    
    def __init__(self, 
                 stock_data_path: str = "stock_data/daily",
                 tech_strength_path: str = "tech_strength/daily"):
        """
        初始化回测验证系统
        
        Args:
            stock_data_path: 股票数据路径
            tech_strength_path: 技术强度数据路径
        """
        self.stock_data_path = stock_data_path
        self.tech_strength_path = tech_strength_path
        self.merged_data = None
        self.validation_results = {}
        
    def load_and_merge_historical_data(self) -> pd.DataFrame:
        """加载并合并历史数据"""
        print("=== 加载历史数据 ===")
        
        # 加载股票数据
        stock_files = sorted(glob.glob(f"{self.stock_data_path}/*.xlsx"))
        tech_files = sorted(glob.glob(f"{self.tech_strength_path}/*.xlsx"))
        
        print(f"股票数据文件: {len(stock_files)} 个")
        print(f"技术强度数据文件: {len(tech_files)} 个")
        
        # 创建日期映射
        stock_date_map = {}
        for file in stock_files:
            match = re.search(r'(\d{8})', file)
            if match:
                date_str = match.group(1)
                formatted_date = f'{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}'
                stock_date_map[formatted_date] = file
        
        tech_date_map = {}
        for file in tech_files:
            match = re.search(r'(\d{4}-\d{2}-\d{2})', file)
            if match:
                tech_date_map[match.group(1)] = file
        
        # 找到重叠日期
        overlap_dates = sorted(set(stock_date_map.keys()) & set(tech_date_map.keys()))
        print(f"重叠日期: {len(overlap_dates)} 天")
        print(f"日期范围: {overlap_dates[0]} ~ {overlap_dates[-1]}")
        
        # 合并数据
        merged_data_list = []
        
        for date in overlap_dates:
            try:
                # 加载股票数据
                stock_df = pd.read_excel(stock_date_map[date])
                stock_df['日期'] = pd.to_datetime(date)
                stock_df['股票代码'] = stock_df['证券代码']
                
                # 加载技术强度数据
                tech_df = pd.read_excel(tech_date_map[date])
                tech_df['日期'] = pd.to_datetime(date)
                
                # 合并数据
                merged_df = pd.merge(
                    stock_df, tech_df,
                    on=['股票代码', '日期'],
                    how='inner',
                    suffixes=('_stock', '_tech')
                )
                
                if len(merged_df) > 0:
                    merged_data_list.append(merged_df)
                    print(f"✓ {date}: 合并 {len(merged_df)} 条记录")
                else:
                    print(f"✗ {date}: 无匹配数据")
                    
            except Exception as e:
                print(f"✗ {date}: 加载失败 - {e}")
        
        if merged_data_list:
            self.merged_data = pd.concat(merged_data_list, ignore_index=True)
            print(f"\n总计合并数据: {len(self.merged_data)} 条记录")
            print(f"涵盖股票: {self.merged_data['股票代码'].nunique()} 只")
            print(f"时间跨度: {len(overlap_dates)} 天")
        else:
            raise ValueError("没有成功合并任何数据")
        
        return self.merged_data
    
    def validate_data_quality(self) -> Dict:
        """验证数据质量"""
        print("\n=== 数据质量验证 ===")
        
        if self.merged_data is None:
            raise ValueError("请先加载数据")
        
        df = self.merged_data
        quality_report = {}
        
        # 1. 数据完整性检查
        print("1. 数据完整性检查")
        missing_data = df.isnull().sum()
        missing_ratio = (missing_data / len(df) * 100).round(2)
        
        quality_report['missing_data'] = {
            'total_records': len(df),
            'missing_by_column': missing_data[missing_data > 0].to_dict(),
            'missing_ratio': missing_ratio[missing_ratio > 0].to_dict()
        }
        
        print(f"   总记录数: {len(df)}")
        if missing_data.sum() > 0:
            print("   缺失数据:")
            for col, count in missing_data[missing_data > 0].items():
                print(f"     {col}: {count} ({missing_ratio[col]:.2f}%)")
        else:
            print("   ✓ 无缺失数据")
        
        # 2. 数据异常值检查
        print("\n2. 数据异常值检查")
        
        # 涨跌幅异常值
        price_change = df['涨跌幅_stock']
        extreme_changes = (abs(price_change) > 20).sum()
        quality_report['extreme_price_changes'] = {
            'count': int(extreme_changes),
            'ratio': float(extreme_changes / len(df) * 100),
            'max_gain': float(price_change.max()),
            'max_loss': float(price_change.min())
        }
        
        print(f"   极端涨跌幅(>20%): {extreme_changes} 条 ({extreme_changes/len(df)*100:.2f}%)")
        print(f"   最大涨幅: {price_change.max():.2f}%")
        print(f"   最大跌幅: {price_change.min():.2f}%")
        
        # 技术强度分布
        tech_strength = df['技术强度']
        quality_report['tech_strength_stats'] = {
            'mean': float(tech_strength.mean()),
            'std': float(tech_strength.std()),
            'min': int(tech_strength.min()),
            'max': int(tech_strength.max()),
            'high_strength_ratio': float((tech_strength >= 80).sum() / len(df) * 100)
        }
        
        print(f"   技术强度范围: {tech_strength.min()} ~ {tech_strength.max()}")
        print(f"   高技术强度(>=80): {(tech_strength >= 80).sum()} 条 ({(tech_strength >= 80).sum()/len(df)*100:.2f}%)")
        
        # 3. 数据一致性检查
        print("\n3. 数据一致性检查")
        
        # 检查涨跌幅一致性
        stock_change = df['涨跌幅_stock']
        tech_change = df['涨跌幅_tech']
        
        # 计算差异
        change_diff = abs(stock_change - tech_change)
        inconsistent = (change_diff > 0.01).sum()  # 差异超过0.01%
        
        quality_report['data_consistency'] = {
            'price_change_inconsistent': int(inconsistent),
            'consistency_ratio': float((len(df) - inconsistent) / len(df) * 100),
            'avg_difference': float(change_diff.mean())
        }
        
        print(f"   涨跌幅不一致: {inconsistent} 条 ({inconsistent/len(df)*100:.2f}%)")
        print(f"   平均差异: {change_diff.mean():.4f}%")
        
        self.validation_results['data_quality'] = quality_report
        return quality_report
    
    def analyze_predictive_features(self) -> Dict:
        """分析预测特征的价值"""
        print("\n=== 预测特征价值分析 ===")
        
        if self.merged_data is None:
            raise ValueError("请先加载数据")
        
        df = self.merged_data.copy()
        feature_analysis = {}
        
        # 1. 技术强度与未来收益的关系
        print("1. 技术强度预测价值分析")
        
        # 按股票分组，计算下一日收益
        df = df.sort_values(['股票代码', '日期'])
        df['next_day_return'] = df.groupby('股票代码')['涨跌幅_stock'].shift(-1)
        
        # 去除无下一日数据的记录
        df_analysis = df.dropna(subset=['next_day_return'])
        
        # 技术强度分组分析
        df_analysis['tech_strength_group'] = pd.cut(
            df_analysis['技术强度'], 
            bins=[0, 40, 60, 80, 100], 
            labels=['低(≤40)', '中(41-60)', '高(61-80)', '极高(>80)']
        )
        
        strength_analysis = df_analysis.groupby('tech_strength_group')['next_day_return'].agg([
            'count', 'mean', 'std', 
            lambda x: (x > 0).sum() / len(x) * 100  # 上涨概率
        ]).round(4)
        strength_analysis.columns = ['样本数', '平均收益率', '收益波动率', '上涨概率(%)']
        
        print("   技术强度分组分析:")
        print(strength_analysis)
        
        feature_analysis['tech_strength_predictive'] = {
            'correlation': float(df_analysis['技术强度'].corr(df_analysis['next_day_return'])),
            'group_analysis': strength_analysis.to_dict()
        }
        
        # 2. 连续技术强度特征分析
        print("\n2. 连续技术强度特征分析")
        
        continuous_features = ['连续技术强度3天数', '连续 技术强度5天数', '连续技术强度10天数']
        continuous_analysis = {}
        
        for feature in continuous_features:
            if feature in df_analysis.columns:
                corr = df_analysis[feature].corr(df_analysis['next_day_return'])
                
                # 高连续强度的表现
                high_continuous = df_analysis[df_analysis[feature] >= df_analysis[feature].quantile(0.8)]
                if len(high_continuous) > 0:
                    high_performance = {
                        'avg_return': float(high_continuous['next_day_return'].mean()),
                        'win_rate': float((high_continuous['next_day_return'] > 0).mean() * 100),
                        'sample_count': int(len(high_continuous))
                    }
                else:
                    high_performance = {'avg_return': 0, 'win_rate': 0, 'sample_count': 0}
                
                continuous_analysis[feature] = {
                    'correlation': float(corr),
                    'high_value_performance': high_performance
                }
                
                print(f"   {feature}:")
                print(f"     相关系数: {corr:.4f}")
                print(f"     高值表现: 平均收益{high_performance['avg_return']:.4f}%, 胜率{high_performance['win_rate']:.1f}%")
        
        feature_analysis['continuous_strength'] = continuous_analysis
        
        # 3. 技术指标特征分析
        print("\n3. 技术指标特征组合分析")
        
        if '技术指标特征' in df_analysis.columns:
            # 解析技术指标特征
            df_analysis['tech_feature_str'] = df_analysis['技术指标特征'].astype(str)
            
            # 统计不同技术指标组合的表现
            tech_feature_performance = df_analysis.groupby('tech_feature_str')['next_day_return'].agg([
                'count', 'mean', lambda x: (x > 0).mean() * 100
            ]).round(4)
            tech_feature_performance.columns = ['样本数', '平均收益率', '胜率(%)']
            
            # 只显示样本数>=10的组合
            significant_features = tech_feature_performance[tech_feature_performance['样本数'] >= 10]
            significant_features = significant_features.sort_values('平均收益率', ascending=False)
            
            print("   显著技术指标组合(样本数>=10):")
            print(significant_features.head(10))
            
            feature_analysis['tech_indicators'] = {
                'total_combinations': int(len(tech_feature_performance)),
                'significant_combinations': int(len(significant_features)),
                'top_performers': significant_features.head(5).to_dict()
            }
        
        self.validation_results['feature_analysis'] = feature_analysis
        return feature_analysis
    
    def validate_trading_logic(self) -> Dict:
        """验证交易逻辑"""
        print("\n=== 交易逻辑验证 ===")
        
        if self.merged_data is None:
            raise ValueError("请先加载数据")
        
        df = self.merged_data.copy()
        df = df.sort_values(['股票代码', '日期'])
        df['next_day_return'] = df.groupby('股票代码')['涨跌幅_stock'].shift(-1)
        df_analysis = df.dropna(subset=['next_day_return'])
        
        trading_validation = {}
        
        # 1. 基于技术强度的简单策略验证
        print("1. 技术强度策略验证")
        
        # 策略1: 技术强度>=80买入
        strategy1_signals = df_analysis['技术强度'] >= 80
        strategy1_returns = df_analysis[strategy1_signals]['next_day_return']
        
        if len(strategy1_returns) > 0:
            strategy1_performance = {
                'total_signals': int(len(strategy1_returns)),
                'avg_return': float(strategy1_returns.mean()),
                'win_rate': float((strategy1_returns > 0).mean() * 100),
                'max_gain': float(strategy1_returns.max()),
                'max_loss': float(strategy1_returns.min()),
                'sharpe_ratio': float(strategy1_returns.mean() / strategy1_returns.std()) if strategy1_returns.std() > 0 else 0
            }
            
            print(f"   策略1(技术强度>=80):")
            print(f"     信号数量: {strategy1_performance['total_signals']}")
            print(f"     平均收益: {strategy1_performance['avg_return']:.4f}%")
            print(f"     胜率: {strategy1_performance['win_rate']:.1f}%")
            print(f"     夏普比率: {strategy1_performance['sharpe_ratio']:.4f}")
        else:
            strategy1_performance = {}
        
        trading_validation['strategy1_tech_strength'] = strategy1_performance
        
        # 2. 组合策略验证
        print("\n2. 组合策略验证")
        
        # 策略2: 技术强度>=70 且 连续技术强度3天数>=技术强度*0.8
        if '连续技术强度3天数' in df_analysis.columns:
            strategy2_signals = (
                (df_analysis['技术强度'] >= 70) & 
                (df_analysis['连续技术强度3天数'] >= df_analysis['技术强度'] * 0.8)
            )
            strategy2_returns = df_analysis[strategy2_signals]['next_day_return']
            
            if len(strategy2_returns) > 0:
                strategy2_performance = {
                    'total_signals': int(len(strategy2_returns)),
                    'avg_return': float(strategy2_returns.mean()),
                    'win_rate': float((strategy2_returns > 0).mean() * 100),
                    'sharpe_ratio': float(strategy2_returns.mean() / strategy2_returns.std()) if strategy2_returns.std() > 0 else 0
                }
                
                print(f"   策略2(技术强度>=70 且 连续强度>=80%):")
                print(f"     信号数量: {strategy2_performance['total_signals']}")
                print(f"     平均收益: {strategy2_performance['avg_return']:.4f}%")
                print(f"     胜率: {strategy2_performance['win_rate']:.1f}%")
                print(f"     夏普比率: {strategy2_performance['sharpe_ratio']:.4f}")
            else:
                strategy2_performance = {}
            
            trading_validation['strategy2_combined'] = strategy2_performance
        
        # 3. 基准比较
        print("\n3. 基准比较")
        
        # 随机策略基准
        random_returns = df_analysis['next_day_return'].sample(min(1000, len(df_analysis)))
        benchmark_performance = {
            'avg_return': float(random_returns.mean()),
            'win_rate': float((random_returns > 0).mean() * 100),
            'sharpe_ratio': float(random_returns.mean() / random_returns.std()) if random_returns.std() > 0 else 0
        }
        
        print(f"   随机基准:")
        print(f"     平均收益: {benchmark_performance['avg_return']:.4f}%")
        print(f"     胜率: {benchmark_performance['win_rate']:.1f}%")
        print(f"     夏普比率: {benchmark_performance['sharpe_ratio']:.4f}")
        
        trading_validation['benchmark'] = benchmark_performance
        
        self.validation_results['trading_logic'] = trading_validation
        return trading_validation
    
    def generate_validation_report(self) -> Dict:
        """生成完整的验证报告"""
        print("\n" + "="*60)
        print("📊 回测验证报告")
        print("="*60)
        
        if not self.validation_results:
            print("❌ 请先运行验证分析")
            return {}
        
        report = {
            'validation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_summary': {
                'total_records': len(self.merged_data) if self.merged_data is not None else 0,
                'unique_stocks': self.merged_data['股票代码'].nunique() if self.merged_data is not None else 0,
                'date_range': f"{self.merged_data['日期'].min()} ~ {self.merged_data['日期'].max()}" if self.merged_data is not None else "N/A"
            },
            'validation_results': self.validation_results
        }
        
        # 数据质量评分
        if 'data_quality' in self.validation_results:
            quality = self.validation_results['data_quality']
            missing_score = max(0, 100 - sum(quality['missing_data']['missing_ratio'].values()))
            consistency_score = quality['data_consistency']['consistency_ratio']
            quality_score = (missing_score + consistency_score) / 2
            
            print(f"📈 数据质量评分: {quality_score:.1f}/100")
        
        # 特征价值评分
        if 'feature_analysis' in self.validation_results:
            feature = self.validation_results['feature_analysis']
            tech_strength_corr = abs(feature['tech_strength_predictive']['correlation'])
            feature_score = min(100, tech_strength_corr * 1000)  # 放大相关系数
            
            print(f"🎯 特征价值评分: {feature_score:.1f}/100")
        
        # 策略表现评分
        if 'trading_logic' in self.validation_results:
            trading = self.validation_results['trading_logic']
            if 'strategy1_tech_strength' in trading and trading['strategy1_tech_strength']:
                strategy_win_rate = trading['strategy1_tech_strength']['win_rate']
                benchmark_win_rate = trading['benchmark']['win_rate']
                strategy_score = min(100, (strategy_win_rate / benchmark_win_rate) * 50)
                
                print(f"💰 策略表现评分: {strategy_score:.1f}/100")
        
        return report
    
    def run_complete_validation(self) -> Dict:
        """运行完整的回测验证"""
        print("🚀 开始完整回测验证...")
        
        try:
            # 1. 加载数据
            self.load_and_merge_historical_data()
            
            # 2. 验证数据质量
            self.validate_data_quality()
            
            # 3. 分析特征价值
            self.analyze_predictive_features()
            
            # 4. 验证交易逻辑
            self.validate_trading_logic()
            
            # 5. 生成报告
            report = self.generate_validation_report()
            
            print("\n✅ 回测验证完成!")
            return report
            
        except Exception as e:
            print(f"\n❌ 回测验证失败: {e}")
            return {'error': str(e)}

if __name__ == "__main__":
    # 运行回测验证
    validator = BacktestValidation()
    report = validator.run_complete_validation()
    
    # 保存报告
    import json
    with open('backtest_validation_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n📄 验证报告已保存到: backtest_validation_report.json")
