#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整技术指标分析系统
包含所有技术指标：技术指标特征、趋势组合、成交量是前一日几倍、日内股票标记

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')
from sklearn.preprocessing import LabelEncoder

class CompleteTechnicalAnalysis:
    """完整的技术指标分析系统"""
    
    def __init__(self, tech_strength_path, stock_data_path):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.label_encoders = {}
        self.all_results = []
    
    def load_and_process_data(self):
        """加载并处理数据"""
        print("=" * 80)
        print("🔍 完整技术指标分析系统")
        print("📋 包含：技术指标特征、趋势组合、成交量倍数、日内股票标记")
        print("📋 买卖规则: 当日开盘买入，次日开盘卖出")
        print("=" * 80)
        
        # 加载数据
        print("📂 加载数据...")
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"  技术强度数据: {len(tech_df)} 条")
        print(f"  股票交易数据: {len(stock_df)} 条")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 合并数据
        print("🔗 合并数据...")
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='inner',
            suffixes=('_tech', '_stock')
        )
        
        print(f"  合并后数据: {len(merged_df)} 条")
        
        # 计算交易收益
        print("💹 计算交易收益...")
        trading_results = []
        
        for stock_code, group in merged_df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            for i in range(len(group) - 1):
                current_row = group.iloc[i]
                next_row = group.iloc[i + 1]
                
                buy_price = current_row['开盘价']
                sell_price = next_row['开盘价']
                
                if buy_price > 0 and sell_price > 0:
                    return_rate = (sell_price - buy_price) / buy_price
                    
                    trading_record = {
                        '股票代码': stock_code,
                        '买入日期': current_row['日期'],
                        '收益率': return_rate,
                        '盈利金额': sell_price - buy_price,
                        '是否盈利': return_rate > 0,
                        
                        # 核心技术指标 - 重点分析这些！
                        '技术强度': current_row['技术强度'],
                        '技术指标特征': current_row.get('技术指标特征', ''),
                        '趋势组合': current_row.get('趋势组合', ''),
                        '成交量是前一日几倍': current_row.get('成交量是前一日几倍', 0),
                        '日内股票标记': current_row.get('日内股票标记', ''),
                        
                        # 连续技术强度
                        '连续技术强度3天数': current_row.get('连续技术强度3天数', 0),
                        '连续技术强度5天数': current_row.get('连续技术强度5天数', 0),
                        '连续技术强度10天数': current_row.get('连续技术强度10天数', 0),
                        
                        # 价格指标
                        '开盘价': current_row['开盘价'],
                        '最高价': current_row['最高价'],
                        '最低价': current_row['最低价'],
                        '收盘价': current_row.get('收盘价_stock', current_row.get('收盘价', 0)),
                        '前收盘价': current_row.get('前收盘价', 0),
                        '换手率': current_row.get('换手率', 0),
                        '涨跌幅': current_row.get('涨跌幅_stock', current_row.get('涨跌幅', 0)),
                    }
                    
                    trading_results.append(trading_record)
        
        trading_df = pd.DataFrame(trading_results)
        print(f"  生成交易记录: {len(trading_df)} 笔")
        
        if len(trading_df) > 0:
            win_rate = trading_df['是否盈利'].mean()
            avg_return = trading_df['收益率'].mean()
            print(f"  整体胜率: {win_rate:.3f} ({win_rate*100:.1f}%)")
            print(f"  平均收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")
        
        return trading_df
    
    def analyze_technical_indicators(self, df):
        """分析技术指标特征"""
        print(f"\n🔍 分析技术指标特征...")
        
        # 处理缺失值
        df['技术指标特征'] = df['技术指标特征'].fillna('Unknown')
        df['趋势组合'] = df['趋势组合'].fillna('Unknown')
        df['日内股票标记'] = df['日内股票标记'].fillna('Unknown')
        
        # 编码分类特征
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        
        for feature in categorical_features:
            if feature not in self.label_encoders:
                self.label_encoders[feature] = LabelEncoder()
                df[f'{feature}_编码'] = self.label_encoders[feature].fit_transform(df[feature].astype(str))
            else:
                df[f'{feature}_编码'] = self.label_encoders[feature].transform(df[feature].astype(str))
        
        # 显示各特征的分布
        print(f"\n📊 技术指标特征分布:")
        
        for feature in ['技术指标特征', '趋势组合', '日内股票标记']:
            print(f"\n  {feature}:")
            value_counts = df[feature].value_counts()
            total = len(df)
            
            for value, count in value_counts.head(10).items():
                percentage = count / total * 100
                print(f"    {value}: {count:,} 条 ({percentage:.1f}%)")
            
            if len(value_counts) > 10:
                print(f"    ... 还有 {len(value_counts) - 10} 个其他值")
        
        return df
    
    def comprehensive_technical_analysis(self, df, min_trades=30, min_win_rate=0.65):
        """全面的技术指标分析"""
        print(f"\n🎯 全面技术指标分析 (最少{min_trades}笔交易, 胜率≥{min_win_rate*100:.0f}%)...")
        
        all_results = []
        
        # 1. 技术指标特征单独分析
        print(f"  📊 技术指标特征分析...")
        if '技术指标特征' in df.columns:
            for value in df['技术指标特征'].unique():
                if pd.notna(value) and value != 'Unknown':
                    subset = df[df['技术指标特征'] == value]
                    if len(subset) >= min_trades:
                        win_rate = subset['是否盈利'].mean()
                        avg_return = subset['收益率'].mean()
                        avg_profit = subset['盈利金额'].mean()
                        
                        if win_rate >= min_win_rate:
                            all_results.append({
                                'type': '技术指标特征',
                                'description': f'技术指标特征={value}',
                                'win_rate': win_rate,
                                'avg_return': avg_return,
                                'avg_profit': avg_profit,
                                'total_trades': len(subset),
                                'score': win_rate * avg_return
                            })
        
        # 2. 趋势组合单独分析
        print(f"  📈 趋势组合分析...")
        if '趋势组合' in df.columns:
            for value in df['趋势组合'].unique():
                if pd.notna(value) and value != 'Unknown':
                    subset = df[df['趋势组合'] == value]
                    if len(subset) >= min_trades:
                        win_rate = subset['是否盈利'].mean()
                        avg_return = subset['收益率'].mean()
                        avg_profit = subset['盈利金额'].mean()
                        
                        if win_rate >= min_win_rate:
                            all_results.append({
                                'type': '趋势组合',
                                'description': f'趋势组合={value}',
                                'win_rate': win_rate,
                                'avg_return': avg_return,
                                'avg_profit': avg_profit,
                                'total_trades': len(subset),
                                'score': win_rate * avg_return
                            })
        
        # 3. 日内股票标记分析
        print(f"  🏷️ 日内股票标记分析...")
        if '日内股票标记' in df.columns:
            for value in df['日内股票标记'].unique():
                if pd.notna(value) and value != 'Unknown':
                    subset = df[df['日内股票标记'] == value]
                    if len(subset) >= min_trades:
                        win_rate = subset['是否盈利'].mean()
                        avg_return = subset['收益率'].mean()
                        avg_profit = subset['盈利金额'].mean()
                        
                        if win_rate >= min_win_rate:
                            all_results.append({
                                'type': '日内股票标记',
                                'description': f'日内股票标记={value}',
                                'win_rate': win_rate,
                                'avg_return': avg_return,
                                'avg_profit': avg_profit,
                                'total_trades': len(subset),
                                'score': win_rate * avg_return
                            })
        
        # 4. 成交量倍数详细分析
        print(f"  📊 成交量倍数详细分析...")
        if '成交量是前一日几倍' in df.columns:
            # 精确值分析
            volume_values = sorted([v for v in df['成交量是前一日几倍'].unique() if pd.notna(v)])
            for value in volume_values:
                subset = df[df['成交量是前一日几倍'] == value]
                if len(subset) >= min_trades:
                    win_rate = subset['是否盈利'].mean()
                    avg_return = subset['收益率'].mean()
                    avg_profit = subset['盈利金额'].mean()
                    
                    if win_rate >= min_win_rate:
                        all_results.append({
                            'type': '成交量倍数',
                            'description': f'成交量是前一日几倍={value}',
                            'win_rate': win_rate,
                            'avg_return': avg_return,
                            'avg_profit': avg_profit,
                            'total_trades': len(subset),
                            'score': win_rate * avg_return
                        })
        
        # 5. 技术强度与技术指标特征组合
        print(f"  🔍 技术强度+技术指标特征组合...")
        if '技术强度' in df.columns and '技术指标特征' in df.columns:
            tech_strength_values = [28, 42, 57, 71, 85, 100]  # 主要技术强度值
            
            for tech_val in tech_strength_values:
                for indicator_val in df['技术指标特征'].unique():
                    if pd.notna(indicator_val) and indicator_val != 'Unknown':
                        subset = df[(df['技术强度'] == tech_val) & (df['技术指标特征'] == indicator_val)]
                        if len(subset) >= min_trades:
                            win_rate = subset['是否盈利'].mean()
                            avg_return = subset['收益率'].mean()
                            avg_profit = subset['盈利金额'].mean()
                            
                            if win_rate >= min_win_rate:
                                all_results.append({
                                    'type': '技术强度+技术指标',
                                    'description': f'技术强度={tech_val}, 技术指标特征={indicator_val}',
                                    'win_rate': win_rate,
                                    'avg_return': avg_return,
                                    'avg_profit': avg_profit,
                                    'total_trades': len(subset),
                                    'score': win_rate * avg_return
                                })
        
        # 6. 技术强度与趋势组合
        print(f"  📈 技术强度+趋势组合...")
        if '技术强度' in df.columns and '趋势组合' in df.columns:
            tech_strength_values = [28, 42, 57, 71, 85, 100]
            
            for tech_val in tech_strength_values:
                for trend_val in df['趋势组合'].unique():
                    if pd.notna(trend_val) and trend_val != 'Unknown':
                        subset = df[(df['技术强度'] == tech_val) & (df['趋势组合'] == trend_val)]
                        if len(subset) >= min_trades:
                            win_rate = subset['是否盈利'].mean()
                            avg_return = subset['收益率'].mean()
                            avg_profit = subset['盈利金额'].mean()
                            
                            if win_rate >= min_win_rate:
                                all_results.append({
                                    'type': '技术强度+趋势组合',
                                    'description': f'技术强度={tech_val}, 趋势组合={trend_val}',
                                    'win_rate': win_rate,
                                    'avg_return': avg_return,
                                    'avg_profit': avg_profit,
                                    'total_trades': len(subset),
                                    'score': win_rate * avg_return
                                })
        
        # 7. 成交量与技术指标特征组合
        print(f"  💹 成交量+技术指标特征组合...")
        if '成交量是前一日几倍' in df.columns and '技术指标特征' in df.columns:
            volume_values = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5]  # 主要成交量倍数
            
            for vol_val in volume_values:
                for indicator_val in df['技术指标特征'].unique():
                    if pd.notna(indicator_val) and indicator_val != 'Unknown':
                        subset = df[(df['成交量是前一日几倍'] == vol_val) & (df['技术指标特征'] == indicator_val)]
                        if len(subset) >= min_trades:
                            win_rate = subset['是否盈利'].mean()
                            avg_return = subset['收益率'].mean()
                            avg_profit = subset['盈利金额'].mean()
                            
                            if win_rate >= min_win_rate:
                                all_results.append({
                                    'type': '成交量+技术指标',
                                    'description': f'成交量={vol_val}倍, 技术指标特征={indicator_val}',
                                    'win_rate': win_rate,
                                    'avg_return': avg_return,
                                    'avg_profit': avg_profit,
                                    'total_trades': len(subset),
                                    'score': win_rate * avg_return
                                })
        
        # 8. 三重组合：技术强度+成交量+技术指标特征
        print(f"  🎯 三重组合分析...")
        if all(col in df.columns for col in ['技术强度', '成交量是前一日几倍', '技术指标特征']):
            tech_values = [85, 100]  # 高技术强度
            volume_values = [2.0, 2.5, 3.0, 3.5]  # 高成交量
            
            for tech_val in tech_values:
                for vol_val in volume_values:
                    for indicator_val in df['技术指标特征'].unique():
                        if pd.notna(indicator_val) and indicator_val != 'Unknown':
                            subset = df[(df['技术强度'] == tech_val) & 
                                      (df['成交量是前一日几倍'] == vol_val) & 
                                      (df['技术指标特征'] == indicator_val)]
                            if len(subset) >= min_trades:
                                win_rate = subset['是否盈利'].mean()
                                avg_return = subset['收益率'].mean()
                                avg_profit = subset['盈利金额'].mean()
                                
                                if win_rate >= min_win_rate:
                                    all_results.append({
                                        'type': '三重组合',
                                        'description': f'技术强度={tech_val}, 成交量={vol_val}倍, 技术指标={indicator_val}',
                                        'win_rate': win_rate,
                                        'avg_return': avg_return,
                                        'avg_profit': avg_profit,
                                        'total_trades': len(subset),
                                        'score': win_rate * avg_return
                                    })
        
        # 按评分排序
        all_results.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"  发现 {len(all_results)} 个高胜率技术指标组合")
        
        return all_results
    
    def display_results(self, results, top_n=50):
        """显示结果"""
        if not results:
            print("  ⚠️ 没有找到符合条件的组合")
            return
        
        print(f"\n🏆 前{min(top_n, len(results))}个最佳技术指标组合:")
        
        for i, result in enumerate(results[:top_n], 1):
            print(f"  {i:2d}. [{result['type']}] {result['description']}: "
                  f"胜率{result['win_rate']:.3f}({result['win_rate']*100:.1f}%) "
                  f"收益{result['avg_return']:.4f}({result['avg_return']*100:.2f}%) "
                  f"盈利{result['avg_profit']:.2f}元 "
                  f"交易{result['total_trades']}笔")
    
    def save_results(self, results, output_path):
        """保存结果"""
        if not results:
            print("  ⚠️ 没有结果可保存")
            return
        
        results_data = []
        for result in results:
            results_data.append({
                '组合类型': result['type'],
                '组合描述': result['description'],
                '胜率': f"{result['win_rate']*100:.1f}%",
                '平均收益率': f"{result['avg_return']*100:.2f}%",
                '平均盈利金额': f"{result['avg_profit']:.2f}元",
                '总交易次数': result['total_trades'],
                '综合评分': f"{result['score']:.6f}"
            })
        
        results_df = pd.DataFrame(results_data)
        results_df.to_excel(output_path, index=False)
        print(f"💾 保存了 {len(results)} 个技术指标策略到: {output_path}")

def main():
    """主函数"""
    start_time = datetime.now()
    
    try:
        # 1. 初始化分析系统
        analysis_system = CompleteTechnicalAnalysis(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily"
        )
        
        # 2. 加载和处理数据
        trading_df = analysis_system.load_and_process_data()
        
        if len(trading_df) == 0:
            print("❌ 没有有效的交易数据")
            return
        
        # 3. 分析技术指标
        trading_df = analysis_system.analyze_technical_indicators(trading_df)
        
        # 4. 全面技术指标分析
        all_results = analysis_system.comprehensive_technical_analysis(trading_df)
        
        # 5. 显示结果
        analysis_system.display_results(all_results, top_n=50)
        
        # 6. 保存结果
        output_path = f"完整技术指标分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        analysis_system.save_results(all_results, output_path)
        
        end_time = datetime.now()
        
        print(f"\n" + "="*80)
        print(f"🎉 完整技术指标分析完成!")
        print(f"⏱️ 总耗时: {end_time - start_time}")
        print(f"📊 分析了 {len(trading_df):,} 笔实际交易")
        print(f"🎯 发现 {len(all_results)} 个高胜率技术指标策略")
        print(f"📋 包含：技术指标特征、趋势组合、成交量倍数、日内股票标记")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
