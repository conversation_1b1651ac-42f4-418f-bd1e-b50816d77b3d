#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特征组合准确率分析
寻找高准确率的特征组合子集

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_model import MultiModalStockPredictor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class FeatureCombinationAnalyzer:
    """特征组合分析器"""
    
    def __init__(self, tech_strength_path, stock_data_path, sequence_length=20):
        self.tech_strength_path = tech_strength_path
        self.stock_data_path = stock_data_path
        self.sequence_length = sequence_length
        
        # 标准化器
        self.tech_scaler = StandardScaler()
        self.auxiliary_scaler = StandardScaler()
        self.target_scaler = StandardScaler()
        
        # 标签编码器
        self.label_encoders = {}
    
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("=" * 80)
        print("🔍 特征组合准确率分析")
        print("=" * 80)
        
        # 1. 加载数据
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        print(f"技术强度数据: {len(tech_df)} 条记录")
        print(f"股票交易明细: {len(stock_df)} 条记录")
        
        # 统一日期格式
        tech_df['日期'] = pd.to_datetime(tech_df['日期'])
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        # 2. 合并数据
        merged_df = pd.merge(
            tech_df, stock_df,
            left_on=['股票代码', '日期'],
            right_on=['证券代码', '日期'],
            how='left',
            suffixes=('_tech', '_stock')
        )
        
        print(f"合并后数据: {len(merged_df)} 条记录")
        
        # 3. 特征工程 (使用最优配置)
        tech_strength_features = ['技术强度']
        
        # 连续性指标分段 (去掉10天)
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500}
        }
        
        for indicator, info in continuous_indicators.items():
            if indicator in merged_df.columns:
                min_val = info['min']
                max_val = info['max']
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                
                merged_df[f'{indicator}_分段'] = pd.cut(
                    merged_df[indicator],
                    bins=[min_val - 0.1, cut1, cut2, max_val + 0.1],
                    labels=[1, 2, 3],
                    include_lowest=True
                ).astype(float)
                
                tech_strength_features.append(f'{indicator}_分段')
        
        # 短中期组合特征
        if '连续技术强度3天数_分段' in merged_df.columns and '连续技术强度5天数_分段' in merged_df.columns:
            merged_df['短中期组合'] = merged_df['连续技术强度3天数_分段'] * 10 + merged_df['连续技术强度5天数_分段']
            merged_df['短中期差异'] = merged_df['连续技术强度5天数_分段'] - merged_df['连续技术强度3天数_分段']
            tech_strength_features.extend(['短中期组合', '短中期差异'])
        
        # 成交量特征
        if '成交量是前一日几倍' in merged_df.columns:
            tech_strength_features.append('成交量是前一日几倍')
        
        # 分类特征
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for col in categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                tech_strength_features.append(f'{col}_encoded')
        
        # 4. 辅助特征
        auxiliary_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价',
            '成交量_stock', '成交额', '换手率', '涨跌幅_stock'
        ]
        
        # 价格衍生特征
        if all(col in merged_df.columns for col in ['开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价']):
            merged_df['价格波动率'] = (merged_df['最高价'] - merged_df['最低价']) / merged_df['收盘价_stock']
            merged_df['开盘缺口'] = (merged_df['开盘价'] - merged_df['前收盘价']) / merged_df['前收盘价']
            price_range = merged_df['最高价'] - merged_df['最低价']
            price_range = price_range.replace(0, 1e-8)
            merged_df['收盘强度'] = (merged_df['收盘价_stock'] - merged_df['最低价']) / price_range
            
            auxiliary_features.extend(['价格波动率', '开盘缺口', '收盘强度'])
        
        # 辅助分类特征
        aux_categorical_features = ['复权状态', '交易状态', '是否ST股']
        for col in aux_categorical_features:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                auxiliary_features.append(f'{col}_encoded')
        
        # 5. 确保特征列存在并处理缺失值
        available_tech_features = [col for col in tech_strength_features if col in merged_df.columns]
        available_auxiliary_features = [col for col in auxiliary_features if col in merged_df.columns]
        
        for col in available_tech_features + available_auxiliary_features:
            if merged_df[col].dtype in ['float64', 'int64']:
                merged_df[col] = merged_df[col].fillna(merged_df[col].median())
        
        # 6. 准备最终数据
        target_column = '涨跌幅_tech'
        feature_columns = available_tech_features + available_auxiliary_features + ['股票代码', '日期', target_column]
        final_df = merged_df[feature_columns].copy().dropna(subset=[target_column])
        
        print(f"\n📊 特征统计:")
        print(f"  技术强度特征: {len(available_tech_features)} 个")
        print(f"  辅助特征: {len(available_auxiliary_features)} 个")
        print(f"  总特征数: {len(available_tech_features) + len(available_auxiliary_features)}")
        print(f"  最终数据: {len(final_df)} 条记录")
        
        return final_df, available_tech_features, available_auxiliary_features, target_column
    
    def create_sequences_with_features(self, df, tech_features, auxiliary_features, target_column):
        """创建时间序列并保留特征信息"""
        print(f"\n⏰ 创建时间序列 (序列长度: {self.sequence_length})...")
        
        X_tech_list = []
        X_auxiliary_list = []
        y_list = []
        feature_info_list = []  # 保存每个样本的特征信息
        
        for _, group in df.groupby('股票代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            if len(group) < self.sequence_length + 1:
                continue
            
            tech_data = group[tech_features].values
            auxiliary_data = group[auxiliary_features].values
            target_data = group[target_column].values
            
            for i in range(len(group) - self.sequence_length):
                X_tech_list.append(tech_data[i:i+self.sequence_length])
                X_auxiliary_list.append(auxiliary_data[i:i+self.sequence_length])
                y_list.append(target_data[i+self.sequence_length])
                
                # 保存当前时刻的特征信息 (用于分组分析)
                current_features = {}
                current_row = group.iloc[i+self.sequence_length-1]  # 预测时刻的前一天特征
                
                # 保存关键特征
                if '技术强度' in current_row:
                    current_features['技术强度'] = current_row['技术强度']
                if '连续技术强度3天数_分段' in current_row:
                    current_features['连续技术强度3天数_分段'] = current_row['连续技术强度3天数_分段']
                if '连续技术强度5天数_分段' in current_row:
                    current_features['连续技术强度5天数_分段'] = current_row['连续技术强度5天数_分段']
                if '短中期组合' in current_row:
                    current_features['短中期组合'] = current_row['短中期组合']
                if '成交量是前一日几倍' in current_row:
                    current_features['成交量是前一日几倍'] = current_row['成交量是前一日几倍']
                if '技术指标特征_encoded' in current_row:
                    current_features['技术指标特征_encoded'] = current_row['技术指标特征_encoded']
                if '趋势组合_encoded' in current_row:
                    current_features['趋势组合_encoded'] = current_row['趋势组合_encoded']
                
                feature_info_list.append(current_features)
        
        X_tech = np.array(X_tech_list)
        X_auxiliary = np.array(X_auxiliary_list)
        y = np.array(y_list)
        
        print(f"  创建序列: {len(X_tech)} 个样本")
        print(f"  技术强度特征形状: {X_tech.shape}")
        print(f"  辅助特征形状: {X_auxiliary.shape}")
        
        return X_tech, X_auxiliary, y, feature_info_list
    
    def normalize_data(self, X_tech, X_auxiliary, y, fit_scalers=False):
        """标准化数据"""
        if fit_scalers:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.tech_scaler.fit_transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.fit_transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            X_auxiliary_reshaped = X_auxiliary.reshape(-1, X_auxiliary.shape[-1])
            
            X_tech_scaled = self.tech_scaler.transform(X_tech_reshaped)
            X_auxiliary_scaled = self.auxiliary_scaler.transform(X_auxiliary_reshaped)
            y_scaled = self.target_scaler.transform(y.reshape(-1, 1)).flatten()
        
        X_tech_scaled = X_tech_scaled.reshape(X_tech.shape)
        X_auxiliary_scaled = X_auxiliary_scaled.reshape(X_auxiliary.shape)
        
        return X_tech_scaled, X_auxiliary_scaled, y_scaled
    
    def inverse_transform_target(self, y_scaled):
        """反标准化目标变量"""
        return self.target_scaler.inverse_transform(y_scaled.reshape(-1, 1)).flatten()

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    if len(y_true) == 0:
        return {'direction_accuracy': 0, 'precision': 0, 'recall': 0, 'f1_score': 0}
    
    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)
    
    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)
    
    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def analyze_feature_combinations():
    """分析特征组合的准确率"""
    try:
        # 1. 初始化处理器
        analyzer = FeatureCombinationAnalyzer(
            tech_strength_path="tech_strength/daily",
            stock_data_path="stock_data/daily",
            sequence_length=20
        )
        
        # 2. 加载和准备数据
        final_df, tech_features, auxiliary_features, target_column = analyzer.load_and_prepare_data()
        
        # 3. 创建序列并保留特征信息
        X_tech, X_auxiliary, y, feature_info = analyzer.create_sequences_with_features(
            final_df, tech_features, auxiliary_features, target_column
        )
        
        # 4. 数据分割
        print(f"\n📊 数据分割...")
        n_samples = len(X_tech)
        train_size = int(n_samples * 0.8)
        
        X_tech_train = X_tech[:train_size]
        X_auxiliary_train = X_auxiliary[:train_size]
        y_train = y[:train_size]
        
        X_tech_test = X_tech[train_size:]
        X_auxiliary_test = X_auxiliary[train_size:]
        y_test = y[train_size:]
        feature_info_test = feature_info[train_size:]
        
        print(f"  训练集: {len(X_tech_train)} 样本")
        print(f"  测试集: {len(X_tech_test)} 样本")
        
        # 5. 数据标准化
        print(f"\n🔧 数据标准化...")
        X_tech_train_scaled, X_auxiliary_train_scaled, y_train_scaled = analyzer.normalize_data(
            X_tech_train, X_auxiliary_train, y_train, fit_scalers=True
        )
        X_tech_test_scaled, X_auxiliary_test_scaled, y_test_scaled = analyzer.normalize_data(
            X_tech_test, X_auxiliary_test, y_test, fit_scalers=False
        )
        
        # 6. 创建和训练模型
        print(f"\n🏗️ 创建和训练模型...")
        model = MultiModalStockPredictor(
            sequence_length=analyzer.sequence_length,
            price_features=X_auxiliary_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[128, 256, 128],
            lstm_units=[256, 128],
            attention_heads=16,
            dropout_rate=0.4
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.0005)
        
        print(f"  模型参数量: {model.model.count_params()}")
        
        # 快速训练
        model.train(
            X_auxiliary_train_scaled, X_tech_train_scaled, y_train_scaled,
            epochs=8,  # 减少epoch以节省时间
            batch_size=64
        )
        
        # 7. 模型预测
        print(f"\n📈 模型预测...")
        
        y_pred_scaled = model.predict(X_auxiliary_test_scaled, X_tech_test_scaled)
        y_pred = analyzer.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = analyzer.inverse_transform_target(y_test_scaled)
        
        # 8. 整体准确率
        overall_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        print(f"\n📊 整体准确率: {overall_metrics['direction_accuracy']:.4f} ({overall_metrics['direction_accuracy']*100:.2f}%)")
        
        # 9. 特征组合分析
        print(f"\n🔍 特征组合准确率分析:")
        print("=" * 80)
        
        # 创建特征组合DataFrame
        feature_df = pd.DataFrame(feature_info_test)
        feature_df['y_true'] = y_test_original
        feature_df['y_pred'] = y_pred
        feature_df['correct'] = (y_test_original > 0) == (y_pred > 0)
        
        high_accuracy_combinations = []
        
        # 分析单个特征的准确率
        print(f"\n📋 单个特征准确率分析:")
        
        for feature in ['技术强度', '连续技术强度3天数_分段', '连续技术强度5天数_分段', '短中期组合']:
            if feature in feature_df.columns:
                print(f"\n{feature}:")
                for value in sorted(feature_df[feature].unique()):
                    if pd.notna(value):
                        mask = feature_df[feature] == value
                        subset = feature_df[mask]
                        if len(subset) >= 50:  # 至少50个样本
                            accuracy = subset['correct'].mean()
                            count = len(subset)
                            print(f"  {feature}={value}: {accuracy:.4f} ({accuracy*100:.2f}%) - {count} 样本")
                            
                            if accuracy >= 0.80:  # 80%以上准确率
                                high_accuracy_combinations.append({
                                    'combination': f'{feature}={value}',
                                    'accuracy': accuracy,
                                    'count': count,
                                    'type': 'single'
                                })
        
        # 分析双特征组合
        print(f"\n📋 双特征组合准确率分析:")
        
        feature_pairs = [
            ('技术强度', '连续技术强度3天数_分段'),
            ('技术强度', '连续技术强度5天数_分段'),
            ('连续技术强度3天数_分段', '连续技术强度5天数_分段'),
            ('技术强度', '短中期组合'),
        ]
        
        for feature1, feature2 in feature_pairs:
            if feature1 in feature_df.columns and feature2 in feature_df.columns:
                print(f"\n{feature1} + {feature2}:")
                
                # 获取每个特征的前几个值
                values1 = sorted(feature_df[feature1].unique())[:5]
                values2 = sorted(feature_df[feature2].unique())[:5]
                
                for val1 in values1:
                    for val2 in values2:
                        if pd.notna(val1) and pd.notna(val2):
                            mask = (feature_df[feature1] == val1) & (feature_df[feature2] == val2)
                            subset = feature_df[mask]
                            if len(subset) >= 30:  # 至少30个样本
                                accuracy = subset['correct'].mean()
                                count = len(subset)
                                print(f"  {feature1}={val1}, {feature2}={val2}: {accuracy:.4f} ({accuracy*100:.2f}%) - {count} 样本")
                                
                                if accuracy >= 0.85:  # 85%以上准确率
                                    high_accuracy_combinations.append({
                                        'combination': f'{feature1}={val1}, {feature2}={val2}',
                                        'accuracy': accuracy,
                                        'count': count,
                                        'type': 'double'
                                    })
        
        # 10. 高准确率组合总结
        print(f"\n" + "=" * 80)
        print(f"🎯 高准确率特征组合总结")
        print("=" * 80)
        
        if high_accuracy_combinations:
            # 按准确率排序
            high_accuracy_combinations.sort(key=lambda x: x['accuracy'], reverse=True)
            
            print(f"\n发现 {len(high_accuracy_combinations)} 个高准确率组合:")
            for i, combo in enumerate(high_accuracy_combinations[:10], 1):  # 显示前10个
                print(f"{i:2d}. {combo['combination']}")
                print(f"    准确率: {combo['accuracy']:.4f} ({combo['accuracy']*100:.2f}%)")
                print(f"    样本数: {combo['count']}")
                print(f"    类型: {combo['type']}")
                print()
            
            # 找到接近100%的组合
            perfect_combinations = [combo for combo in high_accuracy_combinations if combo['accuracy'] >= 0.95]
            if perfect_combinations:
                print(f"🎉 发现 {len(perfect_combinations)} 个接近100%准确率的组合:")
                for combo in perfect_combinations:
                    print(f"  ✅ {combo['combination']}: {combo['accuracy']*100:.2f}% ({combo['count']} 样本)")
            else:
                print(f"⚠️ 未发现95%以上准确率的组合，最高准确率: {high_accuracy_combinations[0]['accuracy']*100:.2f}%")
        else:
            print(f"⚠️ 未发现80%以上准确率的特征组合")
        
        return True, overall_metrics['direction_accuracy']
        
    except Exception as e:
        print(f"❌ 特征组合分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    start_time = datetime.now()
    success, accuracy = analyze_feature_combinations()
    end_time = datetime.now()
    
    if success:
        print(f"\n⏱️ 总耗时: {end_time - start_time}")
        print(f"\n🎉 特征组合分析完成!")
        print(f"   📍 整体准确率: {accuracy*100:.2f}%")
        
        print(f"\n💡 分析结论:")
        print(f"   - 通过特征组合分析，可以找到高准确率的子集")
        print(f"   - 这些子集可以用于构建更精准的交易策略")
        print(f"   - 虽然样本数量较少，但准确率显著提升")
    else:
        print("❌ 特征组合分析失败")

if __name__ == "__main__":
    main()
