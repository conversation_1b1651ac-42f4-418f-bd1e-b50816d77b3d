#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
预测结果验证系统
将实际结果与预测结果进行对比，验证模型准确性

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class PredictionValidator:
    """预测结果验证器"""
    
    def __init__(self, prediction_dir="预测文件资料", stock_data_dir="stock_data/daily"):
        self.prediction_dir = prediction_dir
        self.stock_data_dir = stock_data_dir
    
    def find_prediction_files(self):
        """查找预测文件"""
        if not os.path.exists(self.prediction_dir):
            print(f"❌ 预测目录不存在: {self.prediction_dir}")
            return []
        
        prediction_files = []
        for file in os.listdir(self.prediction_dir):
            if file.endswith('_上涨概率预测.xlsx') and not file.startswith('~'):
                prediction_files.append(file)
        
        return sorted(prediction_files)
    
    def extract_date_from_filename(self, filename):
        """从文件名提取日期"""
        # 例如: tech_strength_strong_2025-07-01_smart_上涨概率预测.xlsx
        try:
            parts = filename.split('_')
            for part in parts:
                if '-' in part and len(part) == 10:  # YYYY-MM-DD格式
                    return pd.to_datetime(part)
        except:
            pass
        return None
    
    def find_actual_data(self, prediction_date):
        """查找实际数据"""
        if not os.path.exists(self.stock_data_dir):
            print(f"❌ 股票数据目录不存在: {self.stock_data_dir}")
            return None
        
        # 查找预测日期和次日的数据
        prediction_date_str = prediction_date.strftime('%Y-%m-%d')
        next_date = prediction_date + timedelta(days=1)
        next_date_str = next_date.strftime('%Y-%m-%d')
        
        # 查找包含这些日期的文件
        stock_files = [f for f in os.listdir(self.stock_data_dir) if f.endswith('.xlsx')]
        
        actual_data = []
        for file in stock_files:
            try:
                df = pd.read_excel(os.path.join(self.stock_data_dir, file))
                df['日期'] = pd.to_datetime(df['日期'])
                
                # 筛选预测日期和次日的数据
                relevant_data = df[df['日期'].isin([prediction_date, next_date])]
                if len(relevant_data) > 0:
                    actual_data.append(relevant_data)
            except Exception as e:
                continue
        
        if actual_data:
            return pd.concat(actual_data, ignore_index=True)
        return None
    
    def calculate_actual_returns(self, actual_data, prediction_date):
        """计算实际收益率"""
        print(f"📊 计算实际收益率...")
        
        next_date = prediction_date + timedelta(days=1)
        
        actual_returns = []
        
        for stock_code, group in actual_data.groupby('证券代码'):
            group = group.sort_values('日期')
            
            # 查找预测日期和次日的数据
            prediction_day = group[group['日期'] == prediction_date]
            next_day = group[group['日期'] == next_date]
            
            if len(prediction_day) > 0 and len(next_day) > 0:
                buy_price = prediction_day.iloc[0]['开盘价']  # 预测日开盘价买入
                sell_price = next_day.iloc[0]['开盘价']      # 次日开盘价卖出
                
                if buy_price > 0 and sell_price > 0:
                    actual_return = (sell_price - buy_price) / buy_price
                    actual_profit = sell_price - buy_price
                    is_profitable = actual_return > 0
                    
                    actual_returns.append({
                        '股票代码': stock_code,
                        '预测日期': prediction_date,
                        '买入价格': buy_price,
                        '卖出价格': sell_price,
                        '实际收益率': actual_return,
                        '实际盈利金额': actual_profit,
                        '实际是否盈利': is_profitable
                    })
        
        return pd.DataFrame(actual_returns)
    
    def validate_predictions(self, prediction_file):
        """验证预测结果"""
        print("=" * 80)
        print(f"🔍 验证预测结果: {prediction_file}")
        print("=" * 80)
        
        # 1. 加载预测数据
        prediction_path = os.path.join(self.prediction_dir, prediction_file)
        try:
            prediction_df = pd.read_excel(prediction_path)
            print(f"📂 加载预测数据: {len(prediction_df)} 条记录")
        except Exception as e:
            print(f"❌ 加载预测文件失败: {e}")
            return None
        
        # 2. 提取预测日期
        prediction_date = self.extract_date_from_filename(prediction_file)
        if prediction_date is None:
            print(f"❌ 无法从文件名提取日期")
            return None
        
        print(f"📅 预测日期: {prediction_date.strftime('%Y-%m-%d')}")
        
        # 3. 查找实际数据
        actual_data = self.find_actual_data(prediction_date)
        if actual_data is None or len(actual_data) == 0:
            print(f"❌ 未找到对应的实际数据")
            return None
        
        print(f"📊 找到实际数据: {len(actual_data)} 条记录")
        
        # 4. 计算实际收益率
        actual_returns = self.calculate_actual_returns(actual_data, prediction_date)
        if len(actual_returns) == 0:
            print(f"❌ 无法计算实际收益率")
            return None
        
        print(f"💹 计算实际收益: {len(actual_returns)} 只股票")
        
        # 5. 合并预测和实际结果
        merged_df = pd.merge(
            prediction_df, actual_returns,
            on='股票代码',
            how='inner'
        )
        
        if len(merged_df) == 0:
            print(f"❌ 预测数据与实际数据无法匹配")
            return None
        
        print(f"🔗 成功匹配: {len(merged_df)} 只股票")
        
        return merged_df, actual_returns
    
    def analyze_accuracy(self, merged_df):
        """分析预测准确性"""
        print(f"\n📊 预测准确性分析:")
        
        total_stocks = len(merged_df)
        
        # 1. 整体准确性
        correct_predictions = 0
        for _, row in merged_df.iterrows():
            predicted_up = row['明天上涨概率(%)'] > 50
            actual_up = row['实际是否盈利']
            if predicted_up == actual_up:
                correct_predictions += 1
        
        overall_accuracy = correct_predictions / total_stocks
        print(f"  整体预测准确率: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)")
        
        # 2. 按概率区间分析
        print(f"\n  按预测概率区间分析:")
        
        probability_ranges = [
            (95, 100, "完美信号"),
            (85, 95, "优秀信号"),
            (75, 85, "很好信号"),
            (65, 75, "良好信号"),
            (55, 65, "中性信号"),
            (0, 55, "风险信号")
        ]
        
        range_results = []
        
        for min_prob, max_prob, label in probability_ranges:
            if min_prob == 0:
                subset = merged_df[merged_df['明天上涨概率(%)'] < max_prob]
            else:
                subset = merged_df[(merged_df['明天上涨概率(%)'] >= min_prob) & 
                                 (merged_df['明天上涨概率(%)'] < max_prob)]
            
            if len(subset) > 0:
                actual_win_rate = subset['实际是否盈利'].mean()
                avg_predicted_prob = subset['明天上涨概率(%)'].mean()
                avg_actual_return = subset['实际收益率'].mean()
                count = len(subset)
                
                print(f"    {label} ({min_prob}-{max_prob}%): {count}只")
                print(f"      预测平均概率: {avg_predicted_prob:.1f}%")
                print(f"      实际胜率: {actual_win_rate:.3f} ({actual_win_rate*100:.1f}%)")
                print(f"      实际平均收益率: {avg_actual_return:.4f} ({avg_actual_return*100:.2f}%)")
                print(f"      预测偏差: {avg_predicted_prob/100 - actual_win_rate:.3f}")
                print()
                
                range_results.append({
                    'range': label,
                    'count': count,
                    'predicted_prob': avg_predicted_prob,
                    'actual_win_rate': actual_win_rate * 100,
                    'actual_return': avg_actual_return * 100,
                    'bias': avg_predicted_prob - actual_win_rate * 100
                })
        
        # 3. 顶级推荐验证
        print(f"  🏆 顶级推荐验证 (概率≥85%):")
        top_recommendations = merged_df[merged_df['明天上涨概率(%)'] >= 85]
        
        if len(top_recommendations) > 0:
            top_win_rate = top_recommendations['实际是否盈利'].mean()
            top_avg_return = top_recommendations['实际收益率'].mean()
            top_count = len(top_recommendations)
            
            print(f"    推荐股票数: {top_count}只")
            print(f"    实际胜率: {top_win_rate:.3f} ({top_win_rate*100:.1f}%)")
            print(f"    实际平均收益率: {top_avg_return:.4f} ({top_avg_return*100:.2f}%)")
            
            # 显示具体的顶级推荐结果
            print(f"\n    具体结果 (前10只):")
            top_10 = top_recommendations.nlargest(10, '明天上涨概率(%)')
            for i, (_, row) in enumerate(top_10.iterrows(), 1):
                stock_code = row['股票代码']
                stock_name = row.get('股票名称', '')
                predicted_prob = row['明天上涨概率(%)']
                actual_return = row['实际收益率'] * 100
                is_correct = "✅" if row['实际是否盈利'] else "❌"
                
                print(f"      {i:2d}. {is_correct} {stock_code} {stock_name}: "
                      f"预测{predicted_prob:.1f}% → 实际{actual_return:+.2f}%")
        
        return range_results, overall_accuracy
    
    def save_validation_results(self, merged_df, validation_results, output_path):
        """保存验证结果"""
        # 准备输出数据
        output_df = merged_df[[
            '股票代码', '股票名称', 
            '明天上涨概率(%)', '预期收益率(%)', '预测依据',
            '实际收益率', '实际盈利金额', '实际是否盈利',
            '买入价格', '卖出价格'
        ]].copy()
        
        # 添加验证列
        output_df['实际收益率(%)'] = output_df['实际收益率'] * 100
        output_df['预测是否正确'] = (
            (output_df['明天上涨概率(%)'] > 50) == output_df['实际是否盈利']
        )
        output_df['概率偏差'] = output_df['明天上涨概率(%)'] - output_df['实际是否盈利'] * 100
        
        # 重新排列列
        final_columns = [
            '股票代码', '股票名称',
            '明天上涨概率(%)', '实际收益率(%)', 
            '预期收益率(%)', '实际盈利金额',
            '预测是否正确', '概率偏差',
            '预测依据', '买入价格', '卖出价格'
        ]
        
        output_df = output_df[final_columns]
        
        # 按预测概率排序
        output_df = output_df.sort_values('明天上涨概率(%)', ascending=False)
        
        # 保存到Excel
        with pd.ExcelWriter(output_path) as writer:
            output_df.to_excel(writer, sheet_name='验证结果', index=False)
            
            # 添加统计摘要
            if validation_results:
                summary_data = []
                for result in validation_results:
                    summary_data.append({
                        '概率区间': result['range'],
                        '股票数量': result['count'],
                        '预测平均概率(%)': f"{result['predicted_prob']:.1f}",
                        '实际胜率(%)': f"{result['actual_win_rate']:.1f}",
                        '实际平均收益率(%)': f"{result['actual_return']:.2f}",
                        '预测偏差(%)': f"{result['bias']:+.1f}"
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='准确性统计', index=False)
        
        print(f"💾 验证结果已保存到: {output_path}")

def main():
    """主函数"""
    validator = PredictionValidator()
    
    # 1. 查找预测文件
    prediction_files = validator.find_prediction_files()
    
    if not prediction_files:
        print("❌ 未找到预测文件")
        return
    
    print(f"📂 发现 {len(prediction_files)} 个预测文件:")
    for i, file in enumerate(prediction_files, 1):
        print(f"  {i}. {file}")
    
    # 2. 验证每个预测文件
    for prediction_file in prediction_files:
        try:
            print(f"\n" + "="*80)
            
            # 验证预测
            result = validator.validate_predictions(prediction_file)
            
            if result is not None:
                merged_df, actual_returns = result
                
                # 分析准确性
                range_results, overall_accuracy = validator.analyze_accuracy(merged_df)
                
                # 保存验证结果
                output_path = os.path.join(
                    validator.prediction_dir,
                    prediction_file.replace('_上涨概率预测.xlsx', '_预测验证结果.xlsx')
                )
                validator.save_validation_results(merged_df, range_results, output_path)
                
                print(f"✅ {prediction_file} 验证完成")
            else:
                print(f"❌ {prediction_file} 验证失败")
                
        except Exception as e:
            print(f"❌ 验证 {prediction_file} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    print(f"\n🎉 所有预测文件验证完成！")
    print(f"📊 验证结果文件已生成，可查看预测模型的实际表现")

if __name__ == "__main__":
    main()
