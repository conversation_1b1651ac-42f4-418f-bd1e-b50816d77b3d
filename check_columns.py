import pandas as pd
import glob

# 检查股票数据的列名
stock_files = glob.glob('stock_data/daily/*.xlsx')
if stock_files:
    stock_df = pd.read_excel(stock_files[-1])  # 取最新的文件
    print('股票数据列名:')
    for i, col in enumerate(stock_df.columns):
        print(f'{i+1:2d}. "{col}"')

# 检查技术强度数据的列名
tech_files = glob.glob('tech_strength/daily/*.xlsx')
if tech_files:
    tech_df = pd.read_excel(tech_files[-1])  # 取最新的文件
    print('\n技术强度数据的列名:')
    for i, col in enumerate(tech_df.columns):
        print(f'{i+1:2d}. "{col}"')
