#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试增强的股票预测系统
包含新的特征工程和模型架构

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from integrated_data_processor import IntegratedStockDataProcessor
from multimodal_model import MultiModalStockPredictor
from training_system import StockPredictionTrainingSystem

def test_data_processing():
    """测试数据处理流程"""
    print("=" * 60)
    print("🔍 测试数据处理流程")
    print("=" * 60)
    
    try:
        # 创建数据处理器
        data_processor = IntegratedStockDataProcessor(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=20  # 使用较短的序列长度进行快速测试
        )
        
        # 加载数据
        print("1. 加载股票基础数据...")
        stock_df = data_processor.load_stock_data()
        print(f"   股票数据: {len(stock_df)} 条记录")
        
        print("2. 加载技术强度数据...")
        tech_df = data_processor.load_tech_strength_data()
        print(f"   技术强度数据: {len(tech_df)} 条记录")
        
        # 合并数据
        print("3. 合并数据集...")
        merged_df = data_processor.merge_datasets(stock_df, tech_df)
        print(f"   合并后数据: {len(merged_df)} 条记录")
        
        # 创建技术指标
        print("4. 创建价格技术指标...")
        merged_df = data_processor.create_price_features(merged_df)
        
        # 处理技术强度特征
        print("5. 处理技术强度特征...")
        merged_df = data_processor.process_tech_strength_features(merged_df)
        
        # 创建高级组合特征
        print("6. 创建高级组合特征...")
        merged_df = data_processor.create_advanced_combination_features(merged_df)
        
        # 创建统计学特征
        print("7. 创建统计学特征...")
        merged_df = data_processor.create_statistical_features(merged_df)
        
        # 创建市场微观结构特征
        print("8. 创建市场微观结构特征...")
        merged_df = data_processor.create_market_microstructure_features(merged_df)
        
        # 创建行为金融学特征
        print("9. 创建行为金融学特征...")
        merged_df = data_processor.create_behavioral_finance_features(merged_df)
        
        # 准备特征列
        print("10. 准备特征列...")
        merged_df = data_processor.prepare_feature_columns(merged_df)
        
        # 清理数据
        merged_df = merged_df.dropna()
        print(f"    清理后数据: {len(merged_df)} 条记录")
        
        # 显示特征信息
        print(f"\n📊 特征统计:")
        print(f"   价格特征数量: {len(data_processor.price_feature_columns)}")
        print(f"   技术强度特征数量: {len(data_processor.tech_feature_columns)}")
        print(f"   总特征数量: {len(data_processor.price_feature_columns) + len(data_processor.tech_feature_columns)}")
        
        # 显示前几个特征名称
        print(f"\n🔝 前10个价格特征:")
        for i, feature in enumerate(data_processor.price_feature_columns[:10], 1):
            print(f"   {i:2d}. {feature}")
        
        print(f"\n🔝 前10个技术强度特征:")
        for i, feature in enumerate(data_processor.tech_feature_columns[:10], 1):
            print(f"   {i:2d}. {feature}")
        
        return True, data_processor, merged_df
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_model_creation():
    """测试模型创建"""
    print("\n" + "=" * 60)
    print("🏗️ 测试模型创建")
    print("=" * 60)
    
    try:
        # 创建模型 (使用较小的参数进行快速测试)
        model = MultiModalStockPredictor(
            sequence_length=20,
            price_features=35,  # 估计值，实际会根据数据调整
            tech_features=50,   # 估计值，实际会根据数据调整
            cnn_filters=[32, 64],  # 减少滤波器数量
            lstm_units=[64, 32],   # 减少LSTM单元数量
            attention_heads=4,     # 减少注意力头数量
            dropout_rate=0.3
        )
        
        # 构建模型
        print("1. 构建模型架构...")
        model.build_model()
        
        # 编译模型
        print("2. 编译模型...")
        model.compile_model(learning_rate=0.001)
        
        # 显示模型信息
        print("3. 模型信息:")
        model.get_model_summary()
        
        return True, model
        
    except Exception as e:
        print(f"❌ 模型创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_quick_training():
    """快速训练测试"""
    print("\n" + "=" * 60)
    print("🚀 快速训练测试")
    print("=" * 60)
    
    try:
        # 创建训练系统
        training_system = StockPredictionTrainingSystem(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=20,  # 较短序列
            model_save_dir="test_models"
        )
        
        # 创建测试目录
        os.makedirs("test_models", exist_ok=True)
        
        print("开始快速训练测试...")
        
        # 运行训练 (使用很少的epoch进行快速测试)
        results = training_system.run_complete_training(
            cnn_filters=[32, 64],      # 简化CNN
            lstm_units=[64, 32],       # 简化LSTM
            attention_heads=4,         # 减少注意力头
            dropout_rate=0.3,
            learning_rate=0.001,
            epochs=2,                  # 只训练2个epoch进行测试
            batch_size=64,             # 较大批次大小
            validation_split=0.2
        )
        
        print("✅ 快速训练测试完成!")
        print(f"测试结果: {results}")
        
        return True, results
        
    except Exception as e:
        print(f"❌ 快速训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主测试函数"""
    print("🎯 开始测试增强的股票预测系统")
    print("=" * 80)
    
    # 记录开始时间
    start_time = datetime.now()
    
    # 测试1: 数据处理
    success1, data_processor, merged_df = test_data_processing()
    if not success1:
        print("❌ 数据处理测试失败，停止测试")
        return
    
    # 测试2: 模型创建
    success2, model = test_model_creation()
    if not success2:
        print("❌ 模型创建测试失败，停止测试")
        return
    
    # 测试3: 快速训练
    success3, results = test_quick_training()
    if not success3:
        print("❌ 快速训练测试失败")
        return
    
    # 计算总耗时
    end_time = datetime.now()
    total_time = end_time - start_time
    
    print("\n" + "=" * 80)
    print("🎉 所有测试完成!")
    print("=" * 80)
    print(f"总耗时: {total_time}")
    print(f"数据处理: ✅")
    print(f"模型创建: ✅")
    print(f"快速训练: ✅")
    
    if results:
        print(f"\n📊 训练结果摘要:")
        for key, value in results.items():
            if isinstance(value, (int, float)):
                print(f"   {key}: {value:.6f}")

if __name__ == "__main__":
    main()
