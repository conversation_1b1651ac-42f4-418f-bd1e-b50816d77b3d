#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
纯数据挖掘测试 - 只使用数据源中已有的指标
不创造任何新指标，完全基于原始数据进行挖掘

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_model import MultiModalStockPredictor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class PureDataMiningProcessor:
    """纯数据挖掘处理器 - 只使用原始数据源指标"""
    
    def __init__(self, stock_data_path, tech_strength_path, sequence_length=20):
        self.stock_data_path = stock_data_path
        self.tech_strength_path = tech_strength_path
        self.sequence_length = sequence_length
        
        # 标准化器
        self.price_scaler = StandardScaler()
        self.tech_scaler = StandardScaler()
        self.target_scaler = StandardScaler()
        
        # 标签编码器
        self.label_encoders = {}
    
    def load_raw_data(self):
        """加载原始数据"""
        print("加载原始数据源...")
        
        # 加载股票数据
        stock_files = [f for f in os.listdir(self.stock_data_path) if f.endswith('.xlsx')]
        stock_dfs = []
        for file in stock_files:
            df = pd.read_excel(os.path.join(self.stock_data_path, file))
            stock_dfs.append(df)
        stock_df = pd.concat(stock_dfs, ignore_index=True)
        
        # 加载技术强度数据
        tech_files = [f for f in os.listdir(self.tech_strength_path) if f.endswith('.xlsx')]
        tech_dfs = []
        for file in tech_files:
            df = pd.read_excel(os.path.join(self.tech_strength_path, file))
            tech_dfs.append(df)
        tech_df = pd.concat(tech_dfs, ignore_index=True)
        
        print(f"股票数据: {len(stock_df)} 条记录")
        print(f"技术强度数据: {len(tech_df)} 条记录")
        
        return stock_df, tech_df
    
    def prepare_features(self, stock_df, tech_df):
        """准备特征 - 只使用数据源中的原始指标"""
        print("准备原始特征...")
        
        # 合并数据
        merged_df = pd.merge(
            stock_df, tech_df,
            left_on=['证券代码', '日期'],
            right_on=['股票代码', '日期'],
            how='inner',
            suffixes=('_stock', '_tech')
        )
        
        print(f"合并后数据: {len(merged_df)} 条记录")
        
        # 定义原始特征列
        # 股票数据源的数值特征
        stock_features = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '前收盘价',
            '成交量', '成交额', '换手率', '涨跌幅_stock'
        ]
        
        # 技术强度数据源的数值特征
        tech_features = [
            '收盘价_tech', '涨跌幅_tech', '技术强度',
            '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数',
            '成交量是前一日几倍'
        ]
        
        # 分类特征需要编码
        categorical_features = [
            '复权状态', '交易状态', '是否ST股',
            '技术指标特征', '趋势组合', '日内股票标记'
        ]
        
        # 处理分类特征
        for col in categorical_features:
            if col in merged_df.columns:
                # 处理缺失值
                merged_df[col] = merged_df[col].fillna('Unknown')
                
                # 标签编码
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(merged_df[col].astype(str))
                else:
                    merged_df[f'{col}_encoded'] = self.label_encoders[col].transform(merged_df[col].astype(str))
                
                tech_features.append(f'{col}_encoded')
        
        # 确保特征列存在
        available_stock_features = [col for col in stock_features if col in merged_df.columns]
        available_tech_features = [col for col in tech_features if col in merged_df.columns]
        
        print(f"可用股票特征: {len(available_stock_features)} 个")
        print(f"可用技术强度特征: {len(available_tech_features)} 个")
        
        # 显示特征列表
        print("股票特征:", available_stock_features)
        print("技术强度特征:", available_tech_features)
        
        # 处理缺失值
        for col in available_stock_features + available_tech_features:
            if merged_df[col].dtype in ['float64', 'int64']:
                merged_df[col] = merged_df[col].fillna(merged_df[col].median())
        
        # 设置目标变量 (使用股票数据的涨跌幅)
        target_column = '涨跌幅_stock'
        
        # 准备最终数据
        feature_columns = available_stock_features + available_tech_features + ['证券代码', '日期', target_column]
        final_df = merged_df[feature_columns].copy()
        
        # 删除目标变量中的异常值
        final_df = final_df.dropna(subset=[target_column])
        
        print(f"最终数据: {len(final_df)} 条记录")
        
        return final_df, available_stock_features, available_tech_features, target_column
    
    def create_sequences(self, df, stock_features, tech_features, target_column):
        """创建时间序列"""
        print("创建时间序列...")
        
        X_price_list = []
        X_tech_list = []
        y_list = []
        
        # 按股票分组创建序列
        for stock_code, group in df.groupby('证券代码'):
            group = group.sort_values('日期').reset_index(drop=True)
            
            if len(group) < self.sequence_length + 1:
                continue
            
            # 提取特征数据
            price_data = group[stock_features].values
            tech_data = group[tech_features].values
            target_data = group[target_column].values
            
            # 创建序列
            for i in range(len(group) - self.sequence_length):
                X_price_list.append(price_data[i:i+self.sequence_length])
                X_tech_list.append(tech_data[i:i+self.sequence_length])
                y_list.append(target_data[i+self.sequence_length])
        
        X_price = np.array(X_price_list)
        X_tech = np.array(X_tech_list)
        y = np.array(y_list)
        
        print(f"创建了 {len(X_price)} 个序列样本")
        print(f"价格特征形状: {X_price.shape}")
        print(f"技术强度特征形状: {X_tech.shape}")
        print(f"目标形状: {y.shape}")
        
        return X_price, X_tech, y
    
    def normalize_data(self, X_price, X_tech, y, fit_scalers=False):
        """标准化数据"""
        if fit_scalers:
            # 重塑数据进行标准化
            X_price_reshaped = X_price.reshape(-1, X_price.shape[-1])
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            
            # 拟合标准化器
            X_price_scaled = self.price_scaler.fit_transform(X_price_reshaped)
            X_tech_scaled = self.tech_scaler.fit_transform(X_tech_reshaped)
            y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            # 使用已拟合的标准化器
            X_price_reshaped = X_price.reshape(-1, X_price.shape[-1])
            X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])
            
            X_price_scaled = self.price_scaler.transform(X_price_reshaped)
            X_tech_scaled = self.tech_scaler.transform(X_tech_reshaped)
            y_scaled = self.target_scaler.transform(y.reshape(-1, 1)).flatten()
        
        # 重塑回原始形状
        X_price_scaled = X_price_scaled.reshape(X_price.shape)
        X_tech_scaled = X_tech_scaled.reshape(X_tech.shape)
        
        return X_price_scaled, X_tech_scaled, y_scaled
    
    def inverse_transform_target(self, y_scaled):
        """反标准化目标变量"""
        return self.target_scaler.inverse_transform(y_scaled.reshape(-1, 1)).flatten()

def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    true_direction = (y_true > 0).astype(int)
    pred_direction = (y_pred > 0).astype(int)
    
    accuracy = accuracy_score(true_direction, pred_direction)
    precision = precision_score(true_direction, pred_direction, average='weighted', zero_division=0)
    recall = recall_score(true_direction, pred_direction, average='weighted', zero_division=0)
    f1 = f1_score(true_direction, pred_direction, average='weighted', zero_division=0)
    
    return {
        'direction_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def pure_data_mining_test():
    """纯数据挖掘测试"""
    print("=" * 80)
    print("🔍 纯数据挖掘测试 - 只使用数据源原始指标")
    print("=" * 80)
    
    try:
        # 1. 初始化处理器
        processor = PureDataMiningProcessor(
            stock_data_path="stock_data/daily",
            tech_strength_path="tech_strength/daily",
            sequence_length=20
        )
        
        # 2. 加载原始数据
        stock_df, tech_df = processor.load_raw_data()
        
        # 3. 准备特征
        final_df, stock_features, tech_features, target_column = processor.prepare_features(stock_df, tech_df)
        
        # 4. 创建序列
        X_price, X_tech, y = processor.create_sequences(final_df, stock_features, tech_features, target_column)
        
        # 5. 数据分割
        print("\n数据分割...")
        n_samples = len(X_price)
        train_size = int(n_samples * 0.7)
        val_size = int(n_samples * 0.15)
        
        X_price_train = X_price[:train_size]
        X_tech_train = X_tech[:train_size]
        y_train = y[:train_size]
        
        X_price_val = X_price[train_size:train_size+val_size]
        X_tech_val = X_tech[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        
        X_price_test = X_price[train_size+val_size:]
        X_tech_test = X_tech[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"训练集: {len(X_price_train)} 样本")
        print(f"验证集: {len(X_price_val)} 样本")
        print(f"测试集: {len(X_price_test)} 样本")
        
        # 6. 数据标准化
        print("\n数据标准化...")
        X_price_train_scaled, X_tech_train_scaled, y_train_scaled = processor.normalize_data(
            X_price_train, X_tech_train, y_train, fit_scalers=True
        )
        X_price_val_scaled, X_tech_val_scaled, y_val_scaled = processor.normalize_data(
            X_price_val, X_tech_val, y_val, fit_scalers=False
        )
        X_price_test_scaled, X_tech_test_scaled, y_test_scaled = processor.normalize_data(
            X_price_test, X_tech_test, y_test, fit_scalers=False
        )
        
        # 7. 创建和训练模型
        print("\n创建和训练模型...")
        model = MultiModalStockPredictor(
            sequence_length=processor.sequence_length,
            price_features=X_price_train.shape[-1],
            tech_features=X_tech_train.shape[-1],
            cnn_filters=[64, 128, 256],
            lstm_units=[128, 64],
            attention_heads=8,
            dropout_rate=0.3
        )
        
        model.build_model()
        model.compile_model(learning_rate=0.001)
        
        print(f"模型参数量: {model.model.count_params()}")
        
        # 训练模型
        history = model.train(
            X_price_train_scaled, X_tech_train_scaled, y_train_scaled,
            X_price_val_scaled, X_tech_val_scaled, y_val_scaled,
            epochs=15,
            batch_size=128
        )
        
        # 8. 模型评估
        print("\n模型评估...")
        
        # 在测试集上预测
        y_pred_scaled = model.predict(X_price_test_scaled, X_tech_test_scaled)
        
        # 反标准化预测结果
        y_pred = processor.inverse_transform_target(y_pred_scaled.flatten())
        y_test_original = processor.inverse_transform_target(y_test_scaled)
        
        # 计算准确率指标
        direction_metrics = calculate_direction_accuracy(y_test_original, y_pred)
        
        # 9. 结果展示
        print("\n" + "=" * 80)
        print("📊 纯数据挖掘结果")
        print("=" * 80)
        
        print(f"\n🎯 方向预测准确率:")
        print(f"   方向准确率: {direction_metrics['direction_accuracy']:.4f} ({direction_metrics['direction_accuracy']*100:.2f}%)")
        print(f"   精确率: {direction_metrics['precision']:.4f}")
        print(f"   召回率: {direction_metrics['recall']:.4f}")
        print(f"   F1分数: {direction_metrics['f1_score']:.4f}")
        
        print(f"\n📊 特征使用情况:")
        print(f"   股票特征数: {len(stock_features)}")
        print(f"   技术强度特征数: {len(tech_features)}")
        print(f"   总特征数: {len(stock_features) + len(tech_features)}")
        
        print(f"\n📊 数据使用情况:")
        print(f"   训练样本: {len(X_price_train)}")
        print(f"   测试样本: {len(X_price_test)}")
        print(f"   序列长度: {processor.sequence_length}")
        
        # 分析预测分布
        print(f"\n📊 预测分布分析:")
        print(f"   真实涨跌分布: 上涨 {(y_test_original > 0).mean():.2%}, 下跌 {(y_test_original <= 0).mean():.2%}")
        print(f"   预测涨跌分布: 上涨 {(y_pred > 0).mean():.2%}, 下跌 {(y_pred <= 0).mean():.2%}")
        
        return True, direction_metrics['direction_accuracy']
        
    except Exception as e:
        print(f"❌ 纯数据挖掘测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    print("🚀 开始纯数据挖掘测试")
    
    start_time = datetime.now()
    success, accuracy = pure_data_mining_test()
    end_time = datetime.now()
    
    if success:
        print(f"\n⏱️ 总耗时: {end_time - start_time}")
        print(f"\n🎉 纯数据挖掘测试完成!")
        print(f"   📍 最终准确率: {accuracy*100:.2f}%")
        
        # 与之前结果对比
        baseline_accuracy = 64.81
        improvement = (accuracy * 100) - baseline_accuracy
        print(f"\n📈 相比基础版本 ({baseline_accuracy:.2f}%):")
        if improvement > 0:
            print(f"   ✅ 提升了 {improvement:.2f} 个百分点!")
        else:
            print(f"   ⚠️ 下降了 {abs(improvement):.2f} 个百分点")
    else:
        print("❌ 纯数据挖掘测试失败")

if __name__ == "__main__":
    main()
