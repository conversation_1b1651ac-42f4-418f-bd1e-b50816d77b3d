#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全面信号预测系统
充分利用所有特征：技术指标特征、趋势组合、成交量是前一日几倍等

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')
from sklearn.preprocessing import LabelEncoder

class ComprehensiveSignalPredictor:
    """全面信号预测器"""
    
    def __init__(self):
        self.label_encoders = {}
        
        # 全面的预测规则 (利用所有特征)
        self.prediction_rules = [
            # 🎯 完美信号组合 (≥98%)
            {
                'name': '[完美信号] 满分技术+强势趋势+高涨跌幅组合',
                'signal_level': '🎯 完美信号',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111'] and
                                        row.get('涨跌幅_分段', 0) >= 4.0),
                'probability': 0.985,
                'expected_return': 0.1650,
                'confidence': 'PERFECT',
                'description': '技术强度100 + 强势趋势组合 + 高涨跌幅'
            },
            {
                'name': '[完美信号] 连续强势+趋势确认+高涨跌幅组合',
                'signal_level': '🎯 完美信号',
                'condition': lambda row: (row.get('连续技术强度5天数_三分', 0) == 3.0 and
                                        row.get('趋势组合_编码', 0) == 2 and
                                        row.get('涨跌幅_分段', 0) == 5.0),
                'probability': 0.988,
                'expected_return': 0.1811,
                'confidence': 'PERFECT',
                'description': '连续技术强度5天高段 + 趋势组合编码2 + 涨跌幅分段5'
            },
            
            # 💎 钻石级组合 (90-98%)
            {
                'name': '[钻石组合] 满分技术+超强趋势+超高成交量组合',
                'signal_level': '💎 钻石级组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and
                                        row.get('趋势组合', '') in ['111111', '111121'] and
                                        row.get('成交量是前一日几倍', 0) >= 3.0),
                'probability': 0.945,
                'expected_return': 0.0850,
                'confidence': 'EXCELLENT',
                'description': '技术强度100 + 超强趋势 + 成交量≥3.0倍'
            },
            {
                'name': '[钻石组合] 满分技术+强势技术指标+高成交量组合',
                'signal_level': '💎 钻石级组合',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121'] and
                                        row.get('成交量是前一日几倍', 0) >= 2.5),
                'probability': 0.925,
                'expected_return': 0.0720,
                'confidence': 'EXCELLENT',
                'description': '技术强度100 + 强势技术指标 + 成交量≥2.5倍'
            },
            
            # 🥇 黄金信号组合 (85-95%)
            {
                'name': '[黄金信号] 技术强度满分组合',
                'signal_level': '🥇 黄金信号',
                'condition': lambda row: row.get('技术强度', 0) == 100,
                'probability': 0.890,
                'expected_return': 0.0343,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 (满分技术强度)'
            },
            {
                'name': '[黄金信号] 高技术+强势趋势组合',
                'signal_level': '🥇 黄金信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 85 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121']),
                'probability': 0.875,
                'expected_return': 0.0420,
                'confidence': 'EXCELLENT',
                'description': '技术强度≥85 + 强势趋势组合'
            },
            {
                'name': '[黄金信号] 高技术+优质技术指标组合',
                'signal_level': '🥇 黄金信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 85 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121', '121121']),
                'probability': 0.860,
                'expected_return': 0.0380,
                'confidence': 'EXCELLENT',
                'description': '技术强度≥85 + 优质技术指标特征'
            },
            
            # 🔥 热点信号组合 (80-90%)
            {
                'name': '[热点组合] 高技术+超高成交量组合',
                'signal_level': '🔥 热点信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 71 and
                                        row.get('成交量是前一日几倍', 0) >= 3.0),
                'probability': 0.835,
                'expected_return': 0.0520,
                'confidence': 'VERY_GOOD',
                'description': '技术强度≥71 + 成交量≥3.0倍 (超高热度)'
            },
            {
                'name': '[热点组合] 中高技术+高成交量+强势趋势组合',
                'signal_level': '🔥 热点信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 71 and
                                        row.get('成交量是前一日几倍', 0) >= 2.5 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121', '211111']),
                'probability': 0.820,
                'expected_return': 0.0480,
                'confidence': 'VERY_GOOD',
                'description': '技术强度≥71 + 成交量≥2.5倍 + 强势趋势'
            },
            
            # 🥈 稳健信号组合 (75-85%)
            {
                'name': '[稳健信号] 高技术强度组合',
                'signal_level': '🥈 稳健信号',
                'condition': lambda row: row.get('技术强度', 0) == 85,
                'probability': 0.813,
                'expected_return': 0.0203,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 (高技术强度)'
            },
            {
                'name': '[稳健信号] 中高技术+良好趋势组合',
                'signal_level': '🥈 稳健信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 71 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121', '211111', '211121']),
                'probability': 0.785,
                'expected_return': 0.0280,
                'confidence': 'VERY_GOOD',
                'description': '技术强度≥71 + 良好趋势组合'
            },
            {
                'name': '[稳健信号] 中高技术+良好技术指标组合',
                'signal_level': '🥈 稳健信号',
                'condition': lambda row: (row.get('技术强度', 0) >= 71 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121', '121121', '111211', '121211']),
                'probability': 0.770,
                'expected_return': 0.0250,
                'confidence': 'VERY_GOOD',
                'description': '技术强度≥71 + 良好技术指标特征'
            },
            
            # 📊 放量信号组合 (70-80%)
            {
                'name': '[放量信号] 超高成交量组合',
                'signal_level': '📊 放量信号',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) >= 3.0,
                'probability': 0.786,
                'expected_return': 0.0402,
                'confidence': 'GOOD',
                'description': '成交量≥3.0倍 (超高放量)'
            },
            {
                'name': '[放量信号] 高成交量+良好趋势组合',
                'signal_level': '📊 放量信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) >= 2.5 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121', '211111', '211121']),
                'probability': 0.765,
                'expected_return': 0.0380,
                'confidence': 'GOOD',
                'description': '成交量≥2.5倍 + 良好趋势组合'
            },
            {
                'name': '[放量信号] 高成交量+良好技术指标组合',
                'signal_level': '📊 放量信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) >= 2.5 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121', '121121', '111211', '121211']),
                'probability': 0.750,
                'expected_return': 0.0360,
                'confidence': 'GOOD',
                'description': '成交量≥2.5倍 + 良好技术指标特征'
            },
            
            # 🥉 基础信号组合 (65-75%)
            {
                'name': '[基础信号] 中等技术强度组合',
                'signal_level': '🥉 基础信号',
                'condition': lambda row: row.get('技术强度', 0) == 71,
                'probability': 0.744,
                'expected_return': 0.0074,
                'confidence': 'MODERATE',
                'description': '技术强度=71 (中等技术强度)'
            },
            {
                'name': '[基础信号] 中等成交量+趋势组合',
                'signal_level': '🥉 基础信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) >= 2.0 and
                                        row.get('趋势组合', '') in ['111111', '111121', '121111', '121121', '211111', '211121']),
                'probability': 0.720,
                'expected_return': 0.0180,
                'confidence': 'MODERATE',
                'description': '成交量≥2.0倍 + 趋势组合'
            },
            {
                'name': '[基础信号] 中等成交量+技术指标组合',
                'signal_level': '🥉 基础信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) >= 2.0 and
                                        row.get('技术指标特征', '') in ['111111', '121111', '111121', '121121', '111211', '121211']),
                'probability': 0.705,
                'expected_return': 0.0160,
                'confidence': 'MODERATE',
                'description': '成交量≥2.0倍 + 技术指标特征'
            },
            
            # ⚠️ 风险信号组合 (<50%)
            {
                'name': '[风险信号] 低技术强度组合',
                'signal_level': '⚠️ 风险信号',
                'condition': lambda row: row.get('技术强度', 0) <= 42,
                'probability': 0.380,
                'expected_return': -0.0120,
                'confidence': 'RISK',
                'description': '技术强度≤42 (低技术强度，高风险)'
            },
            {
                'name': '[风险信号] 低成交量+弱势趋势组合',
                'signal_level': '⚠️ 风险信号',
                'condition': lambda row: (row.get('成交量是前一日几倍', 0) <= 0.8 and
                                        row.get('趋势组合', '') in ['222222', '222221', '222212', '222211']),
                'probability': 0.350,
                'expected_return': -0.0180,
                'confidence': 'RISK',
                'description': '成交量≤0.8倍 + 弱势趋势组合'
            }
        ]

    def create_comprehensive_features(self, df):
        """创建全面的预测特征"""
        print(f"🔧 创建全面特征...")

        # 1. 处理分类特征编码
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for feature in categorical_features:
            if feature in df.columns:
                df[feature] = df[feature].fillna('Unknown')
                if feature not in self.label_encoders:
                    self.label_encoders[feature] = LabelEncoder()
                    df[f'{feature}_编码'] = self.label_encoders[feature].fit_transform(df[feature].astype(str))

        # 2. 连续技术强度分段
        continuous_indicators = {
            '连续技术强度3天数': {'min': 28, 'max': 300},
            '连续技术强度5天数': {'min': 28, 'max': 500},
            '连续技术强度10天数': {'min': 28, 'max': 956}
        }

        for indicator, info in continuous_indicators.items():
            if indicator in df.columns:
                min_val, max_val = info['min'], info['max']
                range_size = max_val - min_val
                cut1 = min_val + range_size / 3
                cut2 = min_val + 2 * range_size / 3
                df[f'{indicator}_三分'] = pd.cut(
                    df[indicator],
                    bins=[min_val-1, cut1, cut2, max_val+1],
                    labels=[1, 2, 3]
                ).astype(float)

        # 3. 成交量分段
        if '成交量是前一日几倍' in df.columns:
            df['成交量_三分'] = pd.cut(
                df['成交量是前一日几倍'],
                bins=[0, 1.0, 2.0, 4.0],
                labels=[1, 2, 3]
            ).astype(float)

            df['成交量_五分'] = pd.cut(
                df['成交量是前一日几倍'],
                bins=[0, 0.8, 1.2, 1.8, 2.5, 4.0],
                labels=[1, 2, 3, 4, 5]
            ).astype(float)

        # 4. 涨跌幅分段
        if '涨跌幅' in df.columns:
            df['涨跌幅_分段'] = pd.cut(df['涨跌幅'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)

        # 5. 技术强度分段
        if '技术强度' in df.columns:
            df['技术强度_分段'] = pd.cut(
                df['技术强度'],
                bins=[27, 42, 57, 71, 85, 101],
                labels=[1, 2, 3, 4, 5]
            ).astype(float)

        print(f"  全面特征创建完成")
        return df

    def predict_stock(self, row):
        """预测单只股票"""
        # 按优先级检查规则 (从高胜率到低胜率)
        for rule in self.prediction_rules:
            try:
                if rule['condition'](row):
                    return {
                        'rule_name': rule['name'],
                        'signal_level': rule['signal_level'],
                        'predicted_probability': rule['probability'],
                        'predicted_return': rule['expected_return'],
                        'confidence': rule['confidence'],
                        'description': rule['description'],
                        'predicted_up': rule['probability'] > 0.5
                    }
            except:
                continue

        # 默认预测 (无匹配规则)
        return {
            'rule_name': '[无匹配] 市场平均水平',
            'signal_level': '➡️ 普通信号',
            'predicted_probability': 0.536,
            'predicted_return': 0.0022,
            'confidence': 'NEUTRAL',
            'description': '无特定规则匹配，使用市场平均水平',
            'predicted_up': True
        }

    def predict_file(self, file_path):
        """预测单个文件"""
        print("=" * 80)
        print(f"🔮 全面信号预测: {os.path.basename(file_path)}")
        print("=" * 80)

        # 1. 加载数据
        try:
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                print(f"❌ 不支持的文件格式")
                return None
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return None

        print(f"📂 加载数据: {len(df)} 只股票")
        print(f"📋 数据特征: {list(df.columns)}")

        # 2. 创建全面特征
        df = self.create_comprehensive_features(df)

        # 3. 生成预测
        print(f"🎯 生成全面信号预测...")
        predictions = []
        for _, row in df.iterrows():
            pred = self.predict_stock(row)
            predictions.append(pred)

        # 4. 添加预测结果
        df['信号等级'] = [p['signal_level'] for p in predictions]
        df['预测规则'] = [p['rule_name'] for p in predictions]
        df['上涨概率'] = [p['predicted_probability'] for p in predictions]
        df['上涨概率_百分比'] = [p['predicted_probability'] * 100 for p in predictions]
        df['预期收益率'] = [p['predicted_return'] for p in predictions]
        df['预期收益率_百分比'] = [p['predicted_return'] * 100 for p in predictions]
        df['置信等级'] = [p['confidence'] for p in predictions]
        df['预测依据'] = [p['description'] for p in predictions]
        df['预测上涨'] = [p['predicted_up'] for p in predictions]

        return df, predictions

    def analyze_comprehensive_signals(self, df, predictions):
        """分析全面信号分布"""
        print(f"\n📊 全面信号分析:")

        total = len(df)

        # 按信号等级统计
        signal_counts = df['信号等级'].value_counts()

        print(f"\n  🎯 信号等级分布:")
        for signal_level, count in signal_counts.items():
            percentage = count / total * 100
            avg_prob = df[df['信号等级'] == signal_level]['上涨概率'].mean()
            avg_return = df[df['信号等级'] == signal_level]['预期收益率'].mean()
            print(f"    {signal_level}: {count} 只 ({percentage:.1f}%)")
            print(f"      平均概率: {avg_prob:.1%}, 平均预期收益: {avg_return:.2%}")

        # 特征使用统计
        print(f"\n  📋 特征使用统计:")

        # 技术指标特征使用
        if '技术指标特征' in df.columns:
            tech_features = df['技术指标特征'].value_counts().head(10)
            print(f"    主要技术指标特征:")
            for feature, count in tech_features.items():
                percentage = count / total * 100
                print(f"      {feature}: {count} 只 ({percentage:.1f}%)")

        # 趋势组合使用
        if '趋势组合' in df.columns:
            trend_features = df['趋势组合'].value_counts().head(10)
            print(f"    主要趋势组合:")
            for feature, count in trend_features.items():
                percentage = count / total * 100
                print(f"      {feature}: {count} 只 ({percentage:.1f}%)")

        # 成交量分布
        if '成交量是前一日几倍' in df.columns:
            volume_stats = df['成交量是前一日几倍'].describe()
            print(f"    成交量分布:")
            print(f"      平均: {volume_stats['mean']:.2f}倍")
            print(f"      中位数: {volume_stats['50%']:.2f}倍")
            print(f"      最大: {volume_stats['max']:.2f}倍")

        # 高价值信号统计
        print(f"\n  💎 高价值信号统计:")
        perfect_signals = df[df['信号等级'] == '🎯 完美信号']
        diamond_signals = df[df['信号等级'] == '💎 钻石级组合']
        golden_signals = df[df['信号等级'] == '🥇 黄金信号']
        hot_signals = df[df['信号等级'] == '🔥 热点信号']
        stable_signals = df[df['信号等级'] == '🥈 稳健信号']

        print(f"    🎯 完美信号: {len(perfect_signals)} 只")
        print(f"    💎 钻石级组合: {len(diamond_signals)} 只")
        print(f"    🥇 黄金信号: {len(golden_signals)} 只")
        print(f"    🔥 热点信号: {len(hot_signals)} 只")
        print(f"    🥈 稳健信号: {len(stable_signals)} 只")
        print(f"    高价值信号总计: {len(perfect_signals) + len(diamond_signals) + len(golden_signals) + len(hot_signals) + len(stable_signals)} 只")

        return df

    def show_comprehensive_recommendations(self, df, top_n=25):
        """显示全面推荐"""
        print(f"\n🏆 全面推荐 (按信号等级排序，前{top_n}只):")

        # 定义信号等级优先级
        signal_priority = {
            '🎯 完美信号': 1,
            '💎 钻石级组合': 2,
            '🥇 黄金信号': 3,
            '🔥 热点信号': 4,
            '🥈 稳健信号': 5,
            '📊 放量信号': 6,
            '🥉 基础信号': 7,
            '⚠️ 风险信号': 8,
            '➡️ 普通信号': 9
        }

        # 添加优先级列并排序
        df['信号优先级'] = df['信号等级'].map(signal_priority)
        top_stocks = df.nsmallest(top_n, ['信号优先级', '上涨概率'], keep='first')

        for idx, (_, row) in enumerate(top_stocks.iterrows(), 1):
            stock_code = row.get('股票代码', 'Unknown')
            stock_name = row.get('股票名称', '')
            signal_level = row['信号等级']
            probability = row['上涨概率_百分比']
            expected_return = row['预期收益率_百分比']
            rule_name = row['预测规则']
            description = row['预测依据']

            # 显示关键特征
            tech_strength = row.get('技术强度', 'N/A')
            tech_feature = row.get('技术指标特征', 'N/A')
            trend_combo = row.get('趋势组合', 'N/A')
            volume_ratio = row.get('成交量是前一日几倍', 'N/A')

            print(f"  {idx:2d}. {signal_level} - {stock_code} {stock_name}")
            print(f"      上涨概率: {probability:.1f}% | 预期收益: {expected_return:.2f}%")
            print(f"      预测规则: {rule_name}")
            print(f"      关键特征: 技术强度={tech_strength}, 成交量={volume_ratio}倍")
            print(f"      技术指标: {tech_feature}, 趋势组合: {trend_combo}")
            print(f"      预测依据: {description}")
            print()

    def save_comprehensive_predictions(self, df, output_path):
        """保存全面预测结果"""
        # 选择要保存的列
        output_columns = [
            '股票代码', '股票名称', '日期',
            '信号等级', '预测规则', '上涨概率_百分比', '预期收益率_百分比',
            '置信等级', '预测依据',
            '技术强度', '技术指标特征', '趋势组合', '成交量是前一日几倍', '日内股票标记',
            '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数',
            '收盘价', '涨跌幅'
        ]

        available_columns = [col for col in output_columns if col in df.columns]

        # 重命名列
        column_rename = {
            '上涨概率_百分比': '明天上涨概率(%)',
            '预期收益率_百分比': '预期收益率(%)',
            '置信等级': '置信等级',
            '预测规则': '使用的预测规则',
            '预测依据': '预测依据'
        }

        output_df = df[available_columns].copy()
        output_df = output_df.rename(columns=column_rename)

        # 按信号等级和概率排序
        signal_priority = {
            '🎯 完美信号': 1, '💎 钻石级组合': 2, '🥇 黄金信号': 3,
            '🔥 热点信号': 4, '🥈 稳健信号': 5, '📊 放量信号': 6,
            '🥉 基础信号': 7, '⚠️ 风险信号': 8, '➡️ 普通信号': 9
        }
        output_df['信号优先级'] = output_df['信号等级'].map(signal_priority)
        output_df = output_df.sort_values(['信号优先级', '明天上涨概率(%)'], ascending=[True, False])
        output_df = output_df.drop('信号优先级', axis=1)

        if output_path.endswith('.xlsx'):
            output_df.to_excel(output_path, index=False)
        else:
            output_df.to_csv(output_path, index=False, encoding='utf-8-sig')

        print(f"💾 全面预测结果已保存到: {output_path}")

def main():
    """主函数"""
    predictor = ComprehensiveSignalPredictor()

    # 预测用户数据目录中的文件
    prediction_dir = "预测文件资料"

    if not os.path.exists(prediction_dir):
        print(f"❌ 预测数据目录不存在: {prediction_dir}")
        return

    # 获取目录中的所有Excel文件 (排除已生成的预测文件)
    files = [f for f in os.listdir(prediction_dir)
             if f.endswith(('.xlsx', '.xls', '.csv'))
             and not f.endswith('_上涨概率预测.xlsx')
             and not f.endswith('_高级多特征预测.xlsx')
             and not f.endswith('_信号等级预测.xlsx')
             and not f.endswith('_全面信号预测.xlsx')
             and not f.startswith('~')]

    if not files:
        print(f"❌ 在 {prediction_dir} 目录中未找到数据文件")
        return

    print(f"📂 发现 {len(files)} 个数据文件:")
    for i, file in enumerate(files, 1):
        print(f"  {i}. {file}")

    # 处理每个文件
    for file_name in files:
        file_path = os.path.join(prediction_dir, file_name)

        print(f"\n" + "="*80)
        print(f"📊 全面信号预测: {file_name}")
        print(f"="*80)

        try:
            # 预测
            df, predictions = predictor.predict_file(file_path)

            if df is not None:
                # 分析全面信号
                df = predictor.analyze_comprehensive_signals(df, predictions)

                # 显示全面推荐
                predictor.show_comprehensive_recommendations(df, top_n=20)

                # 保存结果
                if file_path.endswith('.xlsx'):
                    output_path = file_path.replace('.xlsx', '_全面信号预测.xlsx')
                elif file_path.endswith('.xls'):
                    output_path = file_path.replace('.xls', '_全面信号预测.xlsx')
                elif file_path.endswith('.csv'):
                    output_path = file_path.replace('.csv', '_全面信号预测.csv')
                else:
                    output_path = file_path + '_全面信号预测.xlsx'

                predictor.save_comprehensive_predictions(df, output_path)

                print(f"✅ {file_name} 全面信号预测完成！")

        except Exception as e:
            print(f"❌ 预测 {file_name} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue

    print(f"\n🎉 所有文件的全面信号预测完成！")
    print(f"🎯 充分利用了技术指标特征、趋势组合、成交量等所有特征")
    print(f"💎 重点关注完美信号、钻石级组合和黄金信号")

if __name__ == "__main__":
    main()
